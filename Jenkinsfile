#!groovy
env.OCP_SLAVES_CLUSTER_WEIGHT_NUMBER = 1
env.DEFAULT_JFROG_TOOL = "jfrog-cli"
def appData = [
// This is from https://dev.azure.com/SIA-PLS-APP/SIA-PLS-APP/_artifacts/feed/SIAJenkins
    azureArtifactFeed: "SIAJenkins",
    azureOrganization: "SIA-PLS-APP",
    azureProject: "SIA-PLS-APP",
// Email to Ms Team Channel
    msTeamWebHook: "https://sia.webhook.office.com/webhookb2/3df38c95-d581-4020-98c8-64731de97b63@d56c1b0d-cb31-4a85-93e0-11f8d2a215e0/JenkinsCI/bafdcdfc40fe417e8e8183a539d0cc22/99c35bc8-9305-4b73-879f-d1f33b8bfa07",
    nodeVersion: "20.10.0",
// package.json for the application
    npmPackageFilePath: ".",
    deployFileName: "artifact/fmd-api*.zip",
    packageNodeModules: false,
// Pipeline Name found in https://dev.azure.com/SIA-PLS-APP/SIA-PLS-APP/_build
    pipelineReleaseName: "FMD-API",
// This is related to https://dev.azure.com/SIA-PLS-APP/SIA-PLS-APP/_artifacts/feed/SIAJenkins
    uploadToFeed: true
]
if (env.BRANCH_NAME == "develop") {
    echo "Production releases should go through release pipeline"
} else if (env.BRANCH_NAME == "integration") {
    promote(appData)
} else {
    dev_azureNpmPackage(appData)
}
