import * as redis from 'redis';

import { AppConfig } from '../resources';

export class RedisService {
  public static client: ReturnType<typeof redis.createClient>;

  static async init() {
    const { host, port, authKey } = AppConfig.redis;

    this.client = redis.createClient({
      password: authKey,
      pingInterval: 5000,
      socket: {
        host,
        port,
        tls: true,
        keepAlive: 60_000,
      },
    });

    await this.client.connect();

    RedisService.client.on('error', function (error) {
      console.log('RedisService init error', host, port, error);
    });
  }

  public static async getCache(name: string) {
    if (!RedisService.client) {
      console.log('RedisService getCache client not initialized');
      return null;
    }

    try {
      const valueStr = await RedisService.client.get(`[${process.env.APP_ENV}]_${name}`);
      return JSON.parse(valueStr);
    } catch (error) {
      console.log('RedisService getCache error', name, error);
      return null;
    }
  }
}
