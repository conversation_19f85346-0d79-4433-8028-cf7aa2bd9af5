import { DefaultAzureCredential } from '@azure/identity';
import { SecretClient } from '@azure/keyvault-secrets';

export const credential = new DefaultAzureCredential();

const vaultUri = 'https://' + process.env.VAULT_NAME + '.vault.azure.net/';
const client = new SecretClient(vaultUri, credential);

export const getSecretValue = async (secretName: string): Promise<string> => {
  const kvSecret = await client.getSecret(secretName);
  if (kvSecret.value) {
    return Promise.resolve(kvSecret.value);
  }
  console.log('loadAppProperties', 'getSecretValue', 'cannot load secret name', secretName);
  return Promise.resolve('');
};
