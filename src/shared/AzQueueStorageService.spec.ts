import { AzKeyVault } from './AzKeyVault';
import { AzStorageQueue } from './AzQueueStorageService';

jest.mock('@azure/storage-queue', () => {
  return {
    QueueServiceClient: jest.fn().mockImplementation(() => ({
      getQueueClient: jest.fn(),
    })),
  };
});

describe('AzStorageQueue - getServiceClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize QueueServiceClient with the Managed Identity', async () => {
    // Arrange
    const internalStorageAccount = 'plsdevinternalasseta';
    const managedIdentityClientId = '4619a668-eee7-407c-8d1e-fe1dcfb4bec7';

    AzKeyVault.getSecret = jest
      .fn()
      .mockResolvedValueOnce(internalStorageAccount)
      .mockResolvedValueOnce(managedIdentityClientId);

    // Act
    const queueServiceClient = await AzStorageQueue.getServiceClient();

    // Assert
    expect(AzKeyVault.getSecret).toHaveBeenNthCalledWith(1, 'internal-storage-account');
    expect(AzKeyVault.getSecret).toHaveBeenNthCalledWith(2, 'managed-identity-client-id');
    expect(queueServiceClient).toBeDefined();
  });
});

describe('AzStorageQueue', () => {
  beforeEach(() => {
    AzKeyVault.getSecret = jest.fn().mockResolvedValueOnce('connection-string');
    AzStorageQueue.getServiceClient = jest.fn().mockResolvedValueOnce({
      getQueueClient: jest.fn().mockReturnValueOnce({
        sendMessage: jest.fn().mockResolvedValueOnce('the function has been called'),
        updateMessage: jest.fn().mockResolvedValueOnce('the function has updated the message'),
        deleteMessage: jest.fn().mockResolvedValueOnce('the function has deleted the message'),
        receiveMessages: jest.fn().mockResolvedValueOnce('the function has received the message'),
      }),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should send a base64 message to the queue', async () => {
    // Arrange
    const queueName = 'merchant-status-webhook';
    const message = 'SGVsbG8gd29ybGQ='; // Base64 encoded message

    // Act
    const result = await AzStorageQueue.sendBase64Message(queueName, message);

    // Assert
    expect(result).toBe('the function has been called');
  });

  test('should update a message in the queue', async () => {
    // Arrange
    const queueName = 'merchant-status-webhook';
    const messageId = '12345';
    const popReceipt = 'abcdef';
    const newMessage = 'New message';

    // Act
    const result = await AzStorageQueue.updateMessage(queueName, messageId, popReceipt, newMessage);

    // Assert
    expect(result).toBe('the function has updated the message');
  });

  test('should delete a message from the queue', async () => {
    // Arrange
    const queueName = 'merchant-status-webhook';
    const messageId = '12345';
    const popReceipt = 'abcdef';

    // Act
    const result = await AzStorageQueue.deleteMessage(queueName, messageId, popReceipt);

    // Assert
    expect(result).toBe('the function has deleted the message');
  });

  test('should receive a message from the queue', async () => {
    // Arrange
    const queueName = 'merchant-status-webhook';
    const numberOfMessages = 1;
    const visibilityTimeout = 30;

    // Act
    const result = await AzStorageQueue.receiveMessages(queueName, numberOfMessages, visibilityTimeout);

    // Assert
    expect(result).toBe('the function has received the message');
  });
});
