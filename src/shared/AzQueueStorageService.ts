import { QueueServiceClient } from '@azure/storage-queue';
import { AzKeyVault } from './AzKeyVault';
import { logInfo } from '../utils/LogUtils';
import { FunctionName } from '../utils';
import { ManagedIdentityCredential } from '@azure/identity';

export const MERCHANT_WEBHOOK_QUEUE_NAME = 'merchant-status-webhook';

export class AzStorageQueue {
  static queueServiceClient: QueueServiceClient;

  static async getServiceClient() {
    if (!this.queueServiceClient) {
      logInfo('Initialize Azure Storage Queue Service Client', FunctionName.AZ_QUEUE_STORAGE);

      const internalStorageAccount = await AzKeyVault.getSecret('internal-storage-account');
      const managedIdentityClientId = await AzKeyVault.getSecret('managed-identity-client-id');

      this.queueServiceClient = new QueueServiceClient(
        `https://${internalStorageAccount}.queue.core.windows.net`,
        new ManagedIdentityCredential(managedIdentityClientId),
      );
    }

    return this.queueServiceClient;
  }

  static async sendBase64Message(queueName: string, message: any) {
    const jsonMessage = typeof message === 'string' ? message : JSON.stringify(message);
    logInfo(
      FunctionName.AZ_QUEUE_STORAGE,
      `Start sending to queue ${queueName} with message ${jsonMessage}`,
      FunctionName.AZ_QUEUE_STORAGE,
    );
    const base64Message = Buffer.from(jsonMessage).toString('base64');
    const queueClient = (await this.getServiceClient()).getQueueClient(queueName);
    return queueClient.sendMessage(base64Message, {
      messageTimeToLive: -1,
    });
  }

  /**
   *
   * @param queueName
   * @param numberOfMessages - Number of messages to retrieve from the queue, limit 32
   * @param visibilityTimeout - Visibility timeout in seconds, limit 7d
   * @returns
   */
  static async receiveMessages(queueName: string, numberOfMessages = 1, visibilityTimeout = 30) {
    const queueClient = (await this.getServiceClient()).getQueueClient(queueName);
    return queueClient.receiveMessages({ numberOfMessages, visibilityTimeout });
  }

  static async updateMessage(queueName: string, messageId: string, popReceipt: string, newMessage: string) {
    const queueClient = (await this.getServiceClient()).getQueueClient(queueName);
    return queueClient.updateMessage(messageId, popReceipt, newMessage);
  }

  static async deleteMessage(queueName: string, messageId: string, popReceipt: string) {
    const queueClient = (await this.getServiceClient()).getQueueClient(queueName);
    return queueClient.deleteMessage(messageId, popReceipt);
  }
}
