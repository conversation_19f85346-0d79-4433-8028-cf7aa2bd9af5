import { Injectable } from '@nestjs/common';
import axios from 'axios';

import { AppConfig } from '../resources';
import { CacheName } from '../utils/Enum';
import { RedisService } from './RedisService';

export interface ISOCountry {
  name: string;
  codeAlpha2: string;
  codeAlpha3: string;
}

@Injectable()
export class CountryISOService {
  public countriesList = new Array<ISOCountry>(); // Memory cache

  constructor() {
    this.checkCache();
  }

  /**
   * Populate memory cache with countries list
   */
  async checkCache() {
    if (this.countriesList.length === 0) {
      const list = await this.getCountriesList();
      this.countriesList = this.countriesList.concat(list);
      if (this.countriesList.length === 0) {
        console.log('countryISO-service populateCache cant populate countriesList from cache');
      }
    }
  }

  /**
   * Get list of countries mapping from Redis, if not refresh from Fin
   * @returns list of countries mapping
   */
  async getCountriesList() {
    try {
      let list = await RedisService.getCache(CacheName.COUNTRIES);
      if (!list || list.length === 0) {
        console.log('countryISO-service getCountriesList cannot get data from Redis');
        const res = await axios.get(`${AppConfig.finApiUrl}/enumMap/countries`);
        if (res.data.success) {
          list = res.data.data;
        } else {
          console.log('countryISO-service getCountriesList cannot get data from FIN', res.data);
        }
      }

      if (!list) {
        return [];
      }

      return list;
    } catch (e) {
      console.log('countryISO-service getCountriesList cannot get data', e);
      return [];
    }
  }

  /**
   * Get country by name, code alpha 2, code alpha3
   * @param country a country name, or code alpha 2, or code alpha3.
   */
  findCountry(country: string) {
    const formattedCountry = country.toUpperCase();
    if (formattedCountry === 'UNITED STATES' || formattedCountry === 'AMERICA') {
      // Legacy standard
      return this.countriesList.find((c) => c.codeAlpha2 === 'US');
    }

    return this.countriesList.find((item) => {
      return (
        item.codeAlpha2 === formattedCountry ||
        item.codeAlpha3 === formattedCountry ||
        item.name.toUpperCase() === formattedCountry
      );
    });
  }
}
