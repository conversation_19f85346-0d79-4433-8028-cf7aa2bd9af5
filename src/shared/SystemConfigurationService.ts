import axios from 'axios';
import { RedisService } from './RedisService';
import { logError, logWarn } from '../utils/LogUtils';

export default class SystemConfigurationService {
  static async getSystemConfigurations() {
    let systemConfigurations = await RedisService.getCache('SystemConfigurations');
    if (!systemConfigurations) {
      logWarn('SystemConfiguration', 'cannot get config from Redis');

      const res = await axios.get(`${process.env.FINANCE_URL}/system-configurations`);
      if (res.data.success) {
        systemConfigurations = res.data.message;
      } else {
        logError('SystemConfiguration', 'cannot get config from FIN', res.data);
      }
    }

    return systemConfigurations?.versioning_configurations || [];
  }

  static async isFlagEnabled(name, defaultValue = false) {
    const config = await this.getSystemConfigurations();
    if (config) {
      const flag = config.find((item) => item.name === name);
      if (flag) {
        return flag.selected_version === 'ON';
      } else {
        logWarn('SystemConfiguration', 'config not available', name);
      }
    }

    return defaultValue;
  }
}
