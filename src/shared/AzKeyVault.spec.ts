import { SecretClient } from '@azure/keyvault-secrets';
import { AzKeyVault } from '../../src/shared/AzKeyVault';
import { logInfo } from '../../src/utils/LogUtils';

jest.mock('@azure/keyvault-secrets');
jest.mock('@azure/identity'); // Mock Azure identity for complete isolation
jest.mock('../../src/utils/LogUtils', () => ({
  logInfo: jest.fn(),
}));

describe('AzKeyVault', () => {
  const secretName = 'mockSecret';
  const secretValue = 'mySecretValue';

  let mockSetSecret: jest.Mock;
  let mockGetSecret: jest.Mock;

  beforeEach(() => {
    // Clear all instances and calls to constructor and methods
    jest.clearAllMocks();

    // Mock SecretClient's setSecret and getSecret methods
    mockSetSecret = jest.fn();
    mockGetSecret = jest.fn().mockResolvedValue({ value: secretValue });

    // Assign the mock methods to the SecretClient instance
    (SecretClient as jest.Mock).mockImplementation(() => ({
      setSecret: mockSetSecret,
      getSecret: mockGetSecret,
    }));

    // Manually assign a new instance of the mocked SecretClient to AzKeyVault
    AzKeyVault.secretClient = new SecretClient('mock-vault-url', null as any);
  });

  describe('getSecretClient', () => {
    it('should initialize the SecretClient if not already set', () => {
      AzKeyVault.secretClient = undefined; // Reset for initialization test

      // Act
      const client = AzKeyVault.getSecretClient();

      // Assert
      expect(client).toBeInstanceOf(Object);
      expect(logInfo).toHaveBeenCalledWith(`Initialize Key Vault ${process.env.AZ_KEYVAULT_NAME}`, expect.any(String));
    });

    it('should return the existing SecretClient if already initialized', () => {
      // First call initializes it
      const client = AzKeyVault.getSecretClient();

      // Act: Second call should use the existing client
      const secondClient = AzKeyVault.getSecretClient();

      // Assert: Ensure the same instance is used
      expect(secondClient).toBe(client); // Same instance returned
    });
  });

  describe('getSecret', () => {
    it('should return the secret value if found', async () => {
      // Act
      const result = await AzKeyVault.getSecret(secretName);

      // Assert
      expect(mockGetSecret).toHaveBeenCalledWith(secretName);
      expect(result).toBe(secretValue);
    });

    it('should return undefined if the secret is not found', async () => {
      // Arrange
      mockGetSecret.mockResolvedValueOnce({ value: undefined });

      // Act
      const result = await AzKeyVault.getSecret(secretName);

      // Assert
      expect(mockGetSecret).toHaveBeenCalledWith(secretName);
      expect(result).toBeUndefined();
    });
  });

  describe('setSecret', () => {
    it('should set the secret value', async () => {
      // Arrange
      mockSetSecret.mockResolvedValueOnce({ name: secretName, value: secretValue });

      // Act
      const result = await AzKeyVault.setSecret(secretName, secretValue);

      // Assert
      expect(mockSetSecret).toHaveBeenCalledWith(secretName, secretValue);
      expect(result).toEqual({ name: secretName, value: secretValue });
    });
  });
});
