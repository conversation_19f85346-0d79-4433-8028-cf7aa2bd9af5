import { SecretClient } from '@azure/keyvault-secrets';
import { DefaultAzureCredential } from '@azure/identity';
import { logInfo } from '../utils/LogUtils';
import { FunctionName } from '../utils/Enum';

export class AzKeyVault {
  static secretClient: SecretClient;

  static getSecretClient() {
    if (!this.secretClient) {
      logInfo(`Initialize Key Vault ${process.env.AZ_KEYVAULT_NAME}`, FunctionName.AZ_KEY_VAULT);

      const vaultUrl = `https://${process.env.AZ_KEYVAULT_NAME}.vault.azure.net/`;
      this.secretClient = new SecretClient(vaultUrl, new DefaultAzureCredential());
    }

    return this.secretClient;
  }

  static async setSecret(name: string, value: string) {
    return this.getSecretClient().setSecret(name, value);
  }

  static async getSecret(name: string) {
    const secret = await this.getSecretClient().getSecret(name);
    return secret.value;
  }
}
