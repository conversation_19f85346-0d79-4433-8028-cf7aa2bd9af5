import { MerchantDaoService } from '../db/merchant/merchant-dao.service';
import { CrtCounterDecrypt, AppName } from '../utils';
import { logError, logWarn } from '../utils/LogUtils';
import { AzStorageQueue, MERCHANT_WEBHOOK_QUEUE_NAME } from './AzQueueStorageService';
import { getSecretValue } from './KeyVaultService';
import { StatusMappingService } from './StatusMappingService';

export class MerchantWebHookApi {
  static merchantCache = {};
  static MERCHANT_WEBHOOK: 'MerchantWebhook';

  static merchantDaoService: MerchantDaoService;

  static init() {
    this.merchantDaoService = new MerchantDaoService();
  }

  public static async updateStatus(parcel, keyOrManifestStatus, timestamp) {
    const { merchant_account_number, id, tracking_id } = parcel;
    const decryptedMerchantAccNo = CrtCounterDecrypt(merchant_account_number);

    const statusObj = StatusMappingService.getStatus(keyOrManifestStatus);
    if (!statusObj?.blockchain) {
      logWarn(
        `decrypted merchantAccountNumber: ${decryptedMerchantAccNo},
        payload: ${id} ${keyOrManifestStatus}`,
        MerchantWebHookApi.MERCHANT_WEBHOOK,
        `Blockchain status not found`,
      );

      return {
        success: false,
        message: 'Blockchain status not found',
      };
    }

    const merchantApiUrl = await this.getMerchantApiUrl(merchant_account_number);

    if (!merchantApiUrl) {
      logWarn(
        `payload: ${id} ${keyOrManifestStatus}, 
        Merchant ${decryptedMerchantAccNo} does not use webhook`,
        MerchantWebHookApi.MERCHANT_WEBHOOK,
      );

      return {
        success: false,
        message: `Merchant ${decryptedMerchantAccNo} does not use webhook`,
      };
    }

    if (!statusObj?.isMerchantVisible) {
      logWarn(
        `payload: ${id} ${keyOrManifestStatus}`,
        `decrypted merchantAccountNumber: ${decryptedMerchantAccNo}`,
        MerchantWebHookApi.MERCHANT_WEBHOOK,
        `Blockchain status is excluded from merchant webhook: ${keyOrManifestStatus}`,
      );

      return {
        success: false,
        message: `Blockchain status is excluded from merchant webhook: ${keyOrManifestStatus}`,
      };
    }

    const payload = {
      PLS_shipment_booking_reference: id,
      shipment_tracking_id: tracking_id || id,
      status: statusObj.blockchain,
      timestamp,
    };

    try {
      await AzStorageQueue.sendBase64Message(MERCHANT_WEBHOOK_QUEUE_NAME, {
        payload,
        merchantAccountNumber: decryptedMerchantAccNo || '',
        appName: AppName.FMD_API,
      });

      return {
        success: true,
        message: payload,
      };
    } catch (error) {
      logError('error', MerchantWebHookApi.MERCHANT_WEBHOOK, merchant_account_number, id, keyOrManifestStatus, error);

      return {
        success: false,
        message: error,
      };
    }
  }

  public static async getMerchantApiUrl(encryptedAccountNo: string) {
    try {
      let merchant = MerchantWebHookApi.merchantCache[encryptedAccountNo];

      if (merchant?.expiresOn > Date.now()) {
        return merchant.api_url;
      }

      const merchants = await MerchantWebHookApi.merchantDaoService.find({
        query: `SELECT c.api_url, c.merchant_account_number
        FROM c WHERE c.merchant_account_number = @accountNo`,
        parameters: [
          {
            name: '@accountNo',
            value: encryptedAccountNo,
          },
        ],
      });
      merchant = merchants[0];

      if (merchant) {
        merchant.merchant_account_number = CrtCounterDecrypt(merchant.merchant_account_number);
        const merchantSecret = await getSecretValue(merchant.merchant_account_number);

        if (merchantSecret) {
          this.merchantCache[encryptedAccountNo] = {
            ...merchant,
            secret_key: merchantSecret,
            expiresOn: Date.now() + 1 * 60 * 60 * 1000, // Cache for 1 hour
          };

          return merchant.api_url;
        } else {
          logError('error', MerchantWebHookApi.MERCHANT_WEBHOOK, merchant.merchant_account_number, 'Secret not found');
        }
      } else {
        logError('error', MerchantWebHookApi.MERCHANT_WEBHOOK, encryptedAccountNo, 'Merchant not found');
      }
    } catch (err) {
      logError('error', MerchantWebHookApi.MERCHANT_WEBHOOK, encryptedAccountNo, err);
    }

    return null;
  }
}
