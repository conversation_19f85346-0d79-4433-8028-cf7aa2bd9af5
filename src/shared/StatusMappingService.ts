import axios from 'axios';
import { AppConfig } from '../resources';
import { CacheName } from '../utils/Enum';
import { RedisService } from './RedisService';

export interface PlsStatusModel {
  [index: string]: IStatusMap;
}

export interface IStatusMap {
  blockchain: string;
  manifestStt: string;
  isMerchantVisible: boolean;
}

export class StatusMappingService {
  public static parcelStatusMapping: PlsStatusModel; // Store the mapping

  /**
   * Get parcel status mapping from finance-api and map them to memory
   */
  public static async populateStatusMapping() {
    try {
      this.parcelStatusMapping = await RedisService.getCache(CacheName.PARCEL_STATUSES);
      if (!this.parcelStatusMapping || Object.keys(this.parcelStatusMapping).length === 0) {
        const res = await axios.get(`${AppConfig.finApiUrl}/enumMap/parcelStatus`);
        if (res.data?.success) {
          this.parcelStatusMapping = res.data.data;
        }
      }
      if (Object.keys(this.parcelStatusMapping).length > 0) {
        console.log('FMD-api succeed populate status mapping.');
      }
    } catch (error) {
      console.error('StatusMappingService.populateStatusMapping', error);
    }
  }

  public static getStatus(keyOrManifestStatus: string): IStatusMap {
    return (
      this.parcelStatusMapping[keyOrManifestStatus] ||
      Object.values(this.parcelStatusMapping).find((item) => item.manifestStt === keyOrManifestStatus)
    );
  }

  public static getManifestStatus(param: string): string {
    return this.parcelStatusMapping[param]?.manifestStt || '';
  }
}
