import { AppConfig } from './resources';
import { credential, getSecretValue } from './shared/KeyVaultService';

const loadAppProperties = async () => {
  AppConfig.redis.authKey = await getSecretValue('redis-key');

  AppConfig.db.host = await getSecretValue('host');
  AppConfig.db.authKey = await getSecretValue('authKey');
  AppConfig.key.aesPassword = await getSecretValue('aes-password');
  AppConfig.key.aesSalt = await getSecretValue('aes-salt');
  AppConfig.key.aesCounter = Number(await getSecretValue('aes-counter'));

  AppConfig.azureB2C.tenant = await getSecretValue('tenant');
  AppConfig.azureB2C.client_id = await getSecretValue('client-id-azure-b2c');
  AppConfig.azureB2C.client_secret = await getSecretValue('client-secret-azure-b2c');

  AppConfig.keyvaultCredential = credential;
};
export { loadAppProperties };
