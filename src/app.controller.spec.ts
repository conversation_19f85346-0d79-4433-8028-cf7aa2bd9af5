import { CacheModule } from '@nestjs/cache-manager';
import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthService } from './auth/auth.service';
import { FmdCompanyDaoService } from './db/fmd/fmd-company.dao.service';
import { FmdDriverDaoService } from './db/fmd/fmd-driver.dao.service';

describe('AppController', () => {
  let appController: AppController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register()],
      controllers: [AppController],
      providers: [AppService, AuthService, FmdCompanyDaoService, FmdDriverDaoService],
    }).compile();

    appController = app.get<AppController>(AppController);
  });

  describe('root', () => {
    it('should return "PLS FMD API"', () => {
      expect(appController.getHello()).toBe('PLS FMD API');
    });
  });
});
