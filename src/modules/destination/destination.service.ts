import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { AppConfig } from '../../resources';
import { RedisService } from '../../shared/RedisService';
import { CacheName } from '../../utils';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class DestinationService {
  constructor(public httpService: HttpService) {}

  async getAllCountries(): Promise<any> {
    try {
      let destinations = await RedisService.getCache(CacheName.DESTINATION_V2);
      if (!destinations || destinations.length === 0) {
        console.log('DestinationService getAllCountries  cannot get data from Redis');

        const { data } = await lastValueFrom(this.httpService.get(`${AppConfig.finApiUrl}/destinations/v2/items`));

        if (data.success) {
          destinations = data.message;
        } else {
          console.log('DestinationService getAllCountries cannot get data from FIN', data);
        }
      }

      destinations = destinations.filter((item) => item.item_type === 'country');

      return destinations;
    } catch (err) {
      console.log('DestinationService getAllCountries exception', err);
      throw new HttpException('Server error', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
