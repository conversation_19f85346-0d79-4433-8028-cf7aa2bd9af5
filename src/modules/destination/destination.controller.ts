import { Controller, Get, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../../auth/auth.guard';
import { DestinationService } from './destination.service';

@UseGuards(AuthGuard)
@Controller('destination')
export class DestinationController {
  constructor(private destinationService: DestinationService) {}

  @Get('countries')
  async getAllCountries(): Promise<any> {
    return this.destinationService.getAllCountries();
  }
}
