import { DestinationService } from './destination.service';
import { RedisService } from '../../shared/RedisService';
import { of, throwError } from 'rxjs';

const httpService: any = {};
const service = new DestinationService(httpService);

beforeEach(() => {
  jest.clearAllMocks();
  console.log = jest.fn();
});

describe('Test destination service', () => {
  describe('Test get all countries function', () => {
    test('When error happens, throw the error', async () => {
      //Assign
      RedisService.getCache = jest.fn().mockRejectedValue('error');

      //Act & Assert
      expect(async () => {
        await service.getAllCountries();
      }).rejects.toThrowError('Server error');
    });

    test('When call api failed, call log error', async () => {
      //Assign
      RedisService.getCache = jest.fn().mockResolvedValue([]);
      service.httpService.get = jest.fn().mockReturnValueOnce(of({ data: { success: false, message: [] } }));

      //Act
      await service.getAllCountries();

      //Assert
      expect(console.log).toHaveBeenCalledTimes(2);
    });

    test('When call api succeeded, return value as expected', async () => {
      //Assign
      RedisService.getCache = jest.fn().mockResolvedValue([]);
      const destinations = [{ name: 'Singapore', item_type: 'country', codeAlpha2: 'SG' }];
      service.httpService.get = jest.fn().mockReturnValueOnce(of({ data: { success: true, message: destinations } }));

      //Act
      const result = await service.getAllCountries();

      //Assert
      expect(result).toEqual(destinations);
    });
  });
});
