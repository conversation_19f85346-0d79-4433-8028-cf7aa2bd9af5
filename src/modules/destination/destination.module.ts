import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { AuthModule } from 'src/auth/auth.module';
import { DestinationController } from './destination.controller';
import { DestinationService } from './destination.service';

@Module({
  imports: [HttpModule, AuthModule],
  controllers: [DestinationController],
  providers: [DestinationService],
})
export class DestinationModule {}
