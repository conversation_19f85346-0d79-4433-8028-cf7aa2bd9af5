import { SqlQuerySpec } from '@azure/cosmos';
import { Injectable } from '@nestjs/common';
import { Merchant } from '../../db/merchant/merchant-dao.model';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { decryptArray } from '../../utils';

@Injectable()
export class MerchantService {
  private ENCRYPTED_FIELDS = [
    'merchant_name',
    'merchant_account_number',
    'street',
    'city',
    'state',
    'country',
    'name',
    'email',
    'phone_number',
    'invoicing_info',
    'tax',
    'constParty',
    'sapARCode',
    'updatedBy',
    'rti_info',
    'standard',
    'plus',
    'self-collect',
    'freight',
    'postal',
    'deliveryStatus',
    'service_option',
    'surcharges',
    'adminCharge',
    'minAdminCharge',
    'list_merchant_account',
    'name',
    'email',
    'role',
    'deletedBy',
    'invoice_address',
    'invoice_emails',
  ];

  constructor(private merchantDaoService: MerchantDaoService) {}

  public async getByQuery(query: SqlQuerySpec, isDecrypted = true): Promise<Merchant[]> {
    const encryptedMerchants = await this.merchantDaoService.find(query);
    if (encryptedMerchants.length) {
      return isDecrypted ? decryptArray(encryptedMerchants, this.ENCRYPTED_FIELDS) : encryptedMerchants;
    }
  }
}
