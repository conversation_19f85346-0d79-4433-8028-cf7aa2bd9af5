import { SqlQuerySpec } from '@azure/cosmos';
import { Test } from '@nestjs/testing';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { MerchantService } from './merchant.service';

describe(' Test suite', () => {
  let merchantService: MerchantService;
  let merchantDaoService: MerchantDaoService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [MerchantService, MerchantDaoService],
    }).compile();

    merchantService = module.get<MerchantService>(MerchantService);
    merchantDaoService = module.get<MerchantDaoService>(MerchantDaoService);
  });

  it('should be defined', () => {
    expect(MerchantService).toBeDefined();
  });

  describe('getByQuery', () => {
    it('should fail to get merchants by query', async () => {
      // Arrange
      const query: SqlQuerySpec = {
        query: 'SELECT * FROM c',
      };
      merchantDaoService.find = jest.fn().mockResolvedValue([]);

      // Act
      const decryptedMerchants = await merchantService.getByQuery(query, true);
      const encryptedMerchants = await merchantService.getByQuery(query, false);

      // Assert
      expect(encryptedMerchants).not.toBeDefined();
      expect(decryptedMerchants).not.toBeDefined();
    });
    it('should succeed to get merchants by query', async () => {
      // Arrange
      const query: SqlQuerySpec = {
        query: 'SELECT * FROM c',
      };
      merchantDaoService.find = jest.fn().mockResolvedValue([
        {
          id: '',
          merchant_name: 'Nequearcu',
        },
      ]);

      // Act
      const decryptedMerchants = await merchantService.getByQuery(query, true);
      const encryptedMerchants = await merchantService.getByQuery(query, false);

      // Assert
      expect(encryptedMerchants.length).toBeTruthy();
      expect(decryptedMerchants.length).toBeTruthy();
    });
  });
});
