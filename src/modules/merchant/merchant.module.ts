import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { AuthModule } from '../../auth/auth.module';
import { MerchantController } from './merchant.controller';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { MerchantService } from './merchant.service';

@Module({
  imports: [HttpModule, AuthModule],
  controllers: [MerchantController],
  providers: [MerchantService, MerchantDaoService],
})
export class MerchantModule {}
