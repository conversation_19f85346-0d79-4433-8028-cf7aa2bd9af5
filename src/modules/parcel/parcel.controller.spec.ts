import { DbParcel, ManifestItemDaoService } from '../../db/manifest-item';
import { ManifestItemArchiveDaoService } from '../../db/manifest-item-archive';
import { ParcelController } from './parcel.controller';
import { Resource } from '@azure/cosmos';

jest.mock('axios');

beforeEach(() => {
  jest.clearAllMocks();
  console.log = jest.fn();
});

describe('ParcelController', () => {
  let parcelController: ParcelController;
  let manifestItemDaoService: ManifestItemDaoService;
  let manifestItemArchiveDaoService: ManifestItemArchiveDaoService;

  beforeEach(async () => {
    manifestItemArchiveDaoService = new ManifestItemArchiveDaoService();
    manifestItemDaoService = new ManifestItemDaoService(manifestItemArchiveDaoService);
    parcelController = new ParcelController(manifestItemDaoService);
  });

  describe('ParcelController.getParcelById', () => {
    const response: any = {
      status: jest.fn().mockReturnValue({ json: jest.fn() }),
    };

    it('should get parcel by id successfully and return status code 200', async () => {
      // Arrange
      const mockedParcel = {
        id: '123',
        tracking_id: 'tracking123',
      } as DbParcel & Resource;

      jest.spyOn(manifestItemDaoService, 'getParcelByTrackingNoOrId').mockResolvedValueOnce(mockedParcel);

      // Act

      await parcelController.getParcelById('123', response);

      // Assert
      expect(response.status).toHaveBeenCalledWith(200);

      expect(response.status().json).toHaveBeenCalledWith({
        success: true,
        data: mockedParcel,
      });
    });

    it('should return status code 404', async () => {
      // Arrange

      jest.spyOn(manifestItemDaoService, 'getParcelByTrackingNoOrId').mockResolvedValueOnce(null);

      // Act

      await parcelController.getParcelById('123', response);

      // Assert
      expect(response.status).toHaveBeenCalledWith(404);
    });

    it('should get parcel by id successfully and return status code 500', async () => {
      // Arrange
      jest
        .spyOn(manifestItemDaoService, 'getParcelByTrackingNoOrId')
        .mockRejectedValueOnce(new Error('failed to fetch parcel'));

      // Act

      await parcelController.getParcelById('123', response);

      // Assert
      expect(response.status).toHaveBeenCalledWith(500);

      expect(response.status().json).toHaveBeenCalledWith({
        success: false,
        message: 'failed to fetch parcel',
      });
    });
  });
});
