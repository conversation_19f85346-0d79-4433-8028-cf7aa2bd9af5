import { StatusMappingService } from '../../shared/StatusMappingService';
import { returnInvalidScan } from './parcel.utils'; // Adjust the import path as needed

describe('returnInvalidScan', () => {
  it('should return "scanned" when the parcel has the latest tracking status "PICKED_UP"', () => {
    // Arrange
    const parcel = {
      id: '1',
      latest_tracking_status: 'picked_up',
      merchant_account_number: '12345',
      PLS_batch_no: '67890',
    };

    // Mock the StatusMappingService to return the same value
    jest.spyOn(StatusMappingService, 'getManifestStatus').mockImplementation((value) => value);

    // Act
    const result = returnInvalidScan(parcel as any);

    // Assert
    expect(result.message).toBe('scanned');
  });

  it('should return "cancelled" when the parcel has the latest tracking status "CANCELLED"', () => {
    // Arrange
    const parcel = {
      id: '2',
      latest_tracking_status: 'cancelled',
      merchant_account_number: '54321',
      PLS_batch_no: '09876',
    };

    // Mock the StatusMappingService to return the same value
    jest.spyOn(StatusMappingService, 'getManifestStatus').mockImplementation((value) => value);

    // Act
    const result = returnInvalidScan(parcel as any);

    // Assert
    expect(result.message).toBe('cancelled');
  });

  it('should return "invalid" when there is no parcel', () => {
    // Act
    const result = returnInvalidScan();

    // Assert
    expect(result.message).toBe('invalid');
  });

  it('should return "unavailable" for any other status', () => {
    // Arrange
    const parcel = {
      id: '3',
      latest_tracking_status: 'SOME_OTHER_STATUS',
      merchant_account_number: '11111',
      PLS_batch_no: '22222',
    };

    // Act
    const result = returnInvalidScan(parcel as any);

    // Assert
    expect(result.message).toBe('unavailable');
  });
});
