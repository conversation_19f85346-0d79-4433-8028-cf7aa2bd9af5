import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';

import { AuthModule } from '../../auth/auth.module';
import { ManifestItemDaoService } from '../../db/manifest-item';
import { ManifestItemArchiveDaoService } from '../../db/manifest-item-archive';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { MerchantService } from '../merchant/merchant.service';
import { ParcelService } from './parcel.service';
import { ParcelController } from './parcel.controller';

@Module({
  imports: [CacheModule.register(), AuthModule],
  controllers: [ParcelController],
  providers: [
    ParcelService,
    ManifestItemDaoService,
    ManifestItemArchiveDaoService,
    MerchantService,
    MerchantDaoService,
  ],
})
export class ParcelModule {}
