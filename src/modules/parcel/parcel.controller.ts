import { Controller, Get, Param, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../../auth/auth.guard';
import { Response } from 'express';
import { logError, logInfo } from '../../utils/LogUtils';
import { ManifestItemDaoService } from '../../db/manifest-item';

@UseGuards(AuthGuard)
@Controller('parcels')
export class ParcelController {
  constructor(private manifestItemService: ManifestItemDaoService) {}

  @Get(':id')
  async getParcelById(@Param('id') id: string, @Res() res: Response) {
    try {
      const parcel = await this.manifestItemService.getParcelByTrackingNoOrId(id);

      if (!parcel) {
        logInfo(`No parcel with ID ${id} found`, ParcelController.name);
        return res.status(404).json({
          success: false,
          message: `Parcel ID ${id} not found`,
        });
      }

      return res.status(200).json({
        success: true,
        data: parcel,
      });
    } catch (error) {
      logError(`Failed to fetch parcel with ID ${id}`, ParcelController.name, error);
      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
}
