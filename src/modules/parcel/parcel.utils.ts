import { DbParcel } from '../../db/manifest-item';
import { StatusMappingService } from '../../shared/StatusMappingService';
import { ParcelStatus } from '../../utils';

export function returnInvalidScan(parcel?: DbParcel) {
  if (parcel && parcel.latest_tracking_status === StatusMappingService.getManifestStatus(ParcelStatus.PICKED_UP)) {
    return {
      success: false,
      message: 'scanned',
    };
  } else if (
    parcel &&
    parcel.latest_tracking_status === StatusMappingService.getManifestStatus(ParcelStatus.CANCELLED)
  ) {
    return {
      success: false,
      message: 'cancelled',
    };
  } else if (!parcel) {
    return {
      success: false,
      message: 'invalid',
    };
  } else {
    return {
      success: false,
      message: 'unavailable',
    };
  }
}
