import { SqlQuerySpec } from '@azure/cosmos';
import { ManifestItemDaoService } from '../../db/manifest-item';
import { StatusMappingService } from '../../shared/StatusMappingService';
import { ParcelStatus, ValidateResult } from '../../utils';
import * as aesUtil from './../../utils/aesUtils';
import { ParcelService } from './parcel.service';

jest.mock('@azure/cosmos');
jest.mock('./../../db/manifest-item');
jest.mock('./../../utils/aesUtils');

let service: ParcelService;
const manifestItemDao = new ManifestItemDaoService(null);

beforeAll(() => {
  service = new ParcelService(manifestItemDao);
  manifestItemDao.getParcelByTrackingNoOrId = jest.fn();
  manifestItemDao.find = jest.fn();
});

describe('Test parcel service', () => {
  const merchantAccountNo = '111';

  jest.spyOn(aesUtil, 'CrtCounterDecrypt').mockImplementation((value) => value);
  jest.spyOn(aesUtil, 'decryptArray').mockImplementation((value) => value);
  jest.spyOn(StatusMappingService, 'getManifestStatus').mockImplementation((value) => value);
  describe('Test validate scan function', () => {
    test('When there is no parcel with the given tracking no, return parcel not found message', async () => {
      //Assign

      //Act
      const result = await service.validateForScan(undefined, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_NOT_FOUND);
    });

    test('When parcel merchant account is not the same, return error message', async () => {
      //Assign
      const parcelMock = {
        trackingId: 'PLX123',
        id: 'PLX123',
        recipient_addressline1: 'abc',
        country: 'abc',
        merchant_name: 'abc',
        merchant_account_number: '123',
        latest_tracking_status: ParcelStatus.LMD_RECEIVE_BOOKING,
      };

      //Act
      const result = await service.validateForScan(parcelMock, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_NOT_SAME_MERCHANT);
    });

    test('When latest status is booked, return message as expected', async () => {
      //Assign
      const parcelMock = {
        trackingId: 'PLX123',
        id: 'PLX123',
        recipient_addressline1: 'abc',
        country: 'abc',
        merchant_name: 'abc',
        merchant_account_number: '111',
        latest_tracking_status: ParcelStatus.BOOKED,
      };

      //Act
      const result = await service.validateForScan(parcelMock, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_ACCEPTED);
    });

    test('When latest status is received booking, return message as expected', async () => {
      //Assign
      const parcelMock = {
        trackingId: 'PLX123',
        id: 'PLX123',
        recipient_addressline1: 'abc',
        country: 'abc',
        merchant_name: 'abc',
        merchant_account_number: '111',
        latest_tracking_status: ParcelStatus.LMD_RECEIVE_BOOKING,
      };

      //Act
      const result = await service.validateForScan(parcelMock, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_ACCEPTED);
    });

    test('When latest status is pending booking updates, return message as expected', async () => {
      //Assign
      const parcelMock = {
        trackingId: 'PLX123',
        id: 'PLX123',
        recipient_addressline1: 'abc',
        country: 'abc',
        merchant_name: 'abc',
        merchant_account_number: '111',
        latest_tracking_status: ParcelStatus.PENDING_BOOKING_UPDATES,
      };

      //Act
      const result = await service.validateForScan(parcelMock, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_ACCEPTED);
    });

    test('When latest status is rejected booking, return message as expected', async () => {
      //Assign
      const parcelMock = {
        trackingId: 'PLX123',
        id: 'PLX123',
        recipient_addressline1: 'abc',
        country: 'abc',
        merchant_name: 'abc',
        merchant_account_number: '111',
        latest_tracking_status: ParcelStatus.LMD_REJECT_BOOKING,
      };

      //Act
      const result = await service.validateForScan(parcelMock, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_ACCEPTED);
    });

    test('When latest status is cancelled, return message as expected', async () => {
      //Assign
      const parcelMock = {
        trackingId: 'PLX123',
        id: 'PLX123',
        recipient_addressline1: 'abc',
        country: 'abc',
        merchant_name: 'abc',
        merchant_account_number: '111',
        latest_tracking_status: ParcelStatus.CANCELLED,
      };

      //Act
      const result = await service.validateForScan(parcelMock, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_CANCELLED);
    });

    test('When latest status is expired, return message as expected', async () => {
      //Assign
      const parcelMock = {
        trackingId: 'PLX123',
        id: 'PLX123',
        recipient_addressline1: 'abc',
        country: 'abc',
        merchant_name: 'abc',
        merchant_account_number: '111',
        latest_tracking_status: ParcelStatus.EXPIRED,
      };

      //Act
      const result = await service.validateForScan(parcelMock, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_EXPIRED);
    });

    test('When latest status is other status, return message as expected', async () => {
      //Assign
      const parcelMock = {
        trackingId: 'PLX123',
        id: 'PLX123',
        recipient_addressline1: 'abc',
        country: 'abc',
        merchant_name: 'abc',
        merchant_account_number: '111',
        latest_tracking_status: ParcelStatus.RECEIVED_AT_WAREHOUSE,
        tracking_status: [
          {
            status: ParcelStatus.RECEIVED_AT_WAREHOUSE,
            date: new Date('2024-01-01'),
          },
        ],
      };

      //Act
      const result = await service.validateForScan(parcelMock, merchantAccountNo);

      //Assert
      expect(result.message).toBe(ValidateResult.PARCEL_ALREADY_PROCESSED);
    });
  });
  describe('Test getByQuery', () => {
    test('should return encrypt parcels if isDecrypted = false', async () => {
      // Arrange
      manifestItemDao.find = jest.fn().mockResolvedValue([{ parcel: 'abc' }]);

      // Act
      const result = await service.getByQuery('abc' as any, false);

      // Assert
      expect(result).toEqual([{ parcel: 'abc' }]);
    });

    test('should return decrypted parcels if isDecrypted = true', async () => {
      // Arrange
      manifestItemDao.find = jest.fn().mockResolvedValue([{ parcel: 'abc' }]);

      // Act
      const result = await service.getByQuery('abc' as any, true);

      // Assert
      expect(result).toEqual([{ parcel: 'abc' }]);
    });
  });
});
