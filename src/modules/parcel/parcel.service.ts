import { SqlQuerySpec } from '@azure/cosmos';
import { Injectable } from '@nestjs/common';
import { DbParcel, ManifestItemDaoService } from '../../db/manifest-item';
import { StatusMappingService } from '../../shared/StatusMappingService';
import { CrtCounterDecrypt, ParcelStatus, ValidateResult, decryptArray } from '../../utils';

@Injectable()
export class ParcelService {
  private ENCRYPTED_FIELDS = [
    'recipient_first_name',
    'recipient_last_name',
    'phone',
    'phone_country_code',
    'email',
    'recipient_addressline1',
    'recipient_addressline2',
    'recipient_addressline3',
    'city_suburb',
    'state',
    'postcode',
    'country',
    'merchant_name',
    'merchant_account_number',
    'createdBy',
  ];

  constructor(public manifestDaoClient: ManifestItemDaoService) {}

  async getByQuery(query: SqlQuerySpec, isDecrypted = true): Promise<DbParcel[]> {
    const encryptedParcels = await this.manifestDaoClient.find(query);
    if (encryptedParcels.length) {
      return isDecrypted ? decryptArray(encryptedParcels, this.ENCRYPTED_FIELDS) : encryptedParcels;
    }
  }

  async validateForScan(parcel: DbParcel, merchantAccountNo: string) {
    let message = '';
    let warehouseDate: Date;

    if (!parcel) {
      message = ValidateResult.PARCEL_NOT_FOUND;
    } else if (CrtCounterDecrypt(parcel.merchant_account_number) !== merchantAccountNo) {
      message = ValidateResult.PARCEL_NOT_SAME_MERCHANT;
    } else {
      switch (parcel.latest_tracking_status) {
        case StatusMappingService.getManifestStatus(ParcelStatus.BOOKED):
        case StatusMappingService.getManifestStatus(ParcelStatus.LMD_REJECT_BOOKING):
        case StatusMappingService.getManifestStatus(ParcelStatus.LMD_RECEIVE_BOOKING):
        case StatusMappingService.getManifestStatus(ParcelStatus.PENDING_BOOKING_UPDATES):
          message = ValidateResult.PARCEL_ACCEPTED;
          break;
        case StatusMappingService.getManifestStatus(ParcelStatus.CANCELLED):
          message = ValidateResult.PARCEL_CANCELLED;
          break;
        case StatusMappingService.getManifestStatus(ParcelStatus.EXPIRED):
          message = ValidateResult.PARCEL_EXPIRED;
          break;
        default:
          const receivedAtWarehouseDate = parcel.tracking_status.find(
            (item) => item.status === StatusMappingService.getManifestStatus(ParcelStatus.RECEIVED_AT_WAREHOUSE),
          )?.date;
          message = ValidateResult.PARCEL_ALREADY_PROCESSED;
          warehouseDate = receivedAtWarehouseDate;
          break;
      }
    }

    return {
      message,
      data: { warehouseDate },
    };
  }
}
