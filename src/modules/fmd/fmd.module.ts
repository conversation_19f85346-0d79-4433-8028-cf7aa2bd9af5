import { Module } from '@nestjs/common';

import { AuthModule } from '../../auth/auth.module';

import { FmdCompanyDaoService } from '../../db/fmd/fmd-company.dao.service';
import { FmdDriverDaoService } from '../../db/fmd/fmd-driver.dao.service';
import { FmdJobDaoService } from '../../db/fmd/fmd-job.dao.service';
import { ManifestItemDaoService } from '../../db/manifest-item';
import { ManifestItemArchiveDaoService } from '../../db/manifest-item-archive';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { CountryISOService } from '../../shared/CountryISOService';
import { FmdAuthorizationGuard } from './fmd-authorization.guard';
import { MerchantService } from '../merchant/merchant.service';
import { FmdController } from './fmd.controller';
import { FmdService } from './fmd.service';
import { ParcelService } from '../parcel/parcel.service';

@Module({
  imports: [AuthModule],
  providers: [
    MerchantDaoService,
    ManifestItemArchiveDaoService,
    ManifestItemDaoService,
    FmdDriverDaoService,
    FmdCompanyDaoService,
    FmdService,
    ParcelService,
    MerchantService,
    FmdJobDaoService,
    CountryISOService,
    FmdAuthorizationGuard,
  ],
  exports: [FmdDriverDaoService, FmdCompanyDaoService],
  controllers: [FmdController],
})
export class FmdModule {}
