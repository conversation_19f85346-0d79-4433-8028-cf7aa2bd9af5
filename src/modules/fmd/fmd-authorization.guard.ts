import { CanActivate, ExecutionContext, Injectable, ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AppRequest } from '../../auth/auth.request';
import { FmdDriverDaoService } from '../../db/fmd/fmd-driver.dao.service';
import { FmdCompanyDaoService } from '../../db/fmd/fmd-company.dao.service';
import { FmdJobDaoService } from '../../db/fmd/fmd-job.dao.service';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { CountryISOService } from '../../shared/CountryISOService';
import { CrtCounterEncrypt, CrtCounterDecrypt } from '../../utils';
import { FMD_AUTHORIZATION_KEY } from './fmd-authorization.decorator';
import { FmdAuthorizationType, FmdAuthorizationContext } from './fmd-authorization.types';

@Injectable()
export class FmdAuthorizationGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly fmdDriverDaoService: FmdDriverDaoService,
    private readonly fmdCompanyDaoService: FmdCompanyDaoService,
    private readonly fmdJobDaoService: FmdJobDaoService,
    private readonly merchantDaoService: MerchantDaoService,
    private readonly countryIsoService: CountryISOService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const authorizationType = this.reflector.get<FmdAuthorizationType>(
      FMD_AUTHORIZATION_KEY,
      context.getHandler(),
    );

    // If no authorization type is specified, allow access (backward compatibility)
    if (!authorizationType) {
      return true;
    }

    const request = context.switchToHttp().getRequest<AppRequest>();
    
    // Ensure user is authenticated
    if (!request.decoded?.emails?.[0]) {
      throw new UnauthorizedException('User not authenticated');
    }

    const authContext = await this.buildAuthorizationContext(request.decoded.emails[0]);
    
    switch (authorizationType) {
      case FmdAuthorizationType.COMPANY_RESOURCE:
        return this.validateCompanyAccess(request, authContext);
      
      case FmdAuthorizationType.MERCHANT_RESOURCE:
        return this.validateMerchantAccess(request, authContext);
      
      case FmdAuthorizationType.JOB_RESOURCE:
        return this.validateJobAccess(request, authContext);
      
      case FmdAuthorizationType.PARCEL_SCAN:
        return this.validateParcelScanAccess(request, authContext);
      
      default:
        throw new ForbiddenException('Unknown authorization type');
    }
  }

  private async buildAuthorizationContext(userEmail: string): Promise<FmdAuthorizationContext> {
    try {
      const driver = await this.fmdDriverDaoService.getDriver(userEmail);
      if (!driver) {
        throw new ForbiddenException('Driver not found');
      }

      const company = await this.fmdCompanyDaoService.getItem(driver.companyId);
      if (!company) {
        throw new ForbiddenException('Company not found');
      }

      return {
        userEmail,
        driverCompanyId: driver.companyId,
        companyCountry: company.country,
      };
    } catch (error) {
      throw new ForbiddenException('Failed to build authorization context');
    }
  }

  private async validateCompanyAccess(request: AppRequest, authContext: FmdAuthorizationContext): Promise<boolean> {
    const requestedCompanyId = request.params.id;
    
    if (requestedCompanyId !== authContext.driverCompanyId) {
      throw new ForbiddenException('Access denied: You can only access resources from your own company');
    }
    
    return true;
  }

  private async validateMerchantAccess(request: AppRequest, authContext: FmdAuthorizationContext): Promise<boolean> {
    const merchantAccountNo = request.params.merchantAccountNo;
    
    try {
      // Get merchant by account number to check their country
      const merchants = await this.merchantDaoService.find({
        query: 'SELECT * FROM c WHERE c.merchant_account_number = @merchantAccountNo',
        parameters: [
          {
            name: '@merchantAccountNo',
            value: CrtCounterEncrypt(merchantAccountNo),
          },
        ],
      });

      if (!merchants.length) {
        throw new ForbiddenException('Merchant not found');
      }

      const merchant = merchants[0];
      const merchantCountry = CrtCounterDecrypt(merchant.country);
      
      // Normalize country codes for comparison
      const userCountryCode = this.countryIsoService.findCountry(authContext.companyCountry);
      const merchantCountryCode = this.countryIsoService.findCountry(merchantCountry);
      
      if (!userCountryCode || !merchantCountryCode) {
        throw new ForbiddenException('Invalid country information');
      }
      
      if (userCountryCode.codeAlpha2 !== merchantCountryCode.codeAlpha2) {
        throw new ForbiddenException('Access denied: You can only access merchants from your country');
      }
      
      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new ForbiddenException('Failed to validate merchant access');
    }
  }

  private async validateJobAccess(request: AppRequest, authContext: FmdAuthorizationContext): Promise<boolean> {
    const jobId = request.body?.jobId || request.params?.jobId;
    
    if (!jobId) {
      throw new ForbiddenException('Job ID not provided');
    }
    
    try {
      const job = await this.fmdJobDaoService.getItem(jobId, 'job');
      if (!job) {
        throw new ForbiddenException('Job not found');
      }
      
      const jobDriverEmail = CrtCounterDecrypt(job.driver_email);
      
      if (jobDriverEmail !== authContext.userEmail) {
        throw new ForbiddenException('Access denied: You can only access your own jobs');
      }
      
      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new ForbiddenException('Failed to validate job access');
    }
  }

  private async validateParcelScanAccess(request: AppRequest, authContext: FmdAuthorizationContext): Promise<boolean> {
    const { merchantAccountNo } = request.body;
    
    if (!merchantAccountNo) {
      throw new ForbiddenException('Merchant account number not provided');
    }
    
    // Reuse merchant validation logic
    return this.validateMerchantAccess(
      { ...request, params: { merchantAccountNo } } as AppRequest,
      authContext
    );
  }
}
