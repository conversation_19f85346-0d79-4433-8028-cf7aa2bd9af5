import { Body, Controller, Get, Param, Post, Res, UseGuards } from '@nestjs/common';

import { AuthGuard } from '../../auth/auth.guard';
import { FmdCompanyDaoService } from '../../db/fmd/fmd-company.dao.service';

import { Response } from 'express';
import { FmdScanParcelPayload } from 'src/db/fmd/fmd-dao.model';
import { CrtCounterEncrypt } from '../../utils';
import { logError } from '../../utils/LogUtils';
import { MerchantService } from '../merchant/merchant.service';
import { ParcelService } from '../parcel/parcel.service';
import { CreateManifestDocumentDto } from './fmd.dto';
import { FmdService } from './fmd.service';

@UseGuards(AuthGuard)
@Controller('fmd')
export class FmdController {
  constructor(
    private fmdDaoService: FmdCompanyDaoService,
    private fmdService: FmdService,
    private merchantService: MerchantService,
    private parcelService: ParcelService,
  ) {}

  @Get(':id')
  async getJobRequests(@Param('id') id: string) {
    const fmd = await this.fmdDaoService.getItem(id);
    return { success: !!fmd, message: fmd || `No FMD has id is ${id}` };
  }

  @Get(':id/pickup-addresses')
  async getPickupAddresses(@Param('id') companyId: string) {
    return this.fmdService.getMerchantPickupAddressListForCompany(companyId);
  }

  @Get('pickup-parcels/:merchantAccountNo')
  async getPickupParcels(@Param('merchantAccountNo') merchantAccountNo: string) {
    return this.fmdService.getPickupParcelsForMerchant(merchantAccountNo);
  }

  @Post('create-job/:merchantAccountNo')
  async createJob(@Param('merchantAccountNo') merchantAccountNo: string) {
    return this.fmdService.createJob(merchantAccountNo);
  }

  @Post('scan-parcel')
  async scanParcel(@Body() payload: FmdScanParcelPayload) {
    return this.fmdService.scanParcel(payload);
  }

  @Get('picked-up/job')
  async getPickedUpJob() {
    return this.fmdService.getPickedUpJob();
  }

  @Post('manifest-document')
  async createManifestDocument(@Body() createFmdManifestDocumentDto: CreateManifestDocumentDto, @Res() res: Response) {
    const { jobId, parcelIds } = createFmdManifestDocumentDto;

    try {
      const parcels = await this.parcelService.getByQuery({
        query: `SELECT 
          c.id, 
          c.tracking_id,
          c.tracking_no,
          c.weight, 
          c.merchant_name, 
          c.merchant_account_number, 
          c.origin,
          c.destination_group, 
          c.operation_hub,
          c.merchant_declared_currency,
          c.recipient_first_name,
          c.recipient_last_name,
          c.recipient_addressline1,
          c.recipient_addressline2,
          c.recipient_addressline3,
          c.phone_country_code,
          c.phone,
          c.city_suburb,
          c.state,
          c.postcode,
          c.country,
          c.item
          FROM c 
          WHERE ARRAY_CONTAINS(@parcelIds, c.id) OR ARRAY_CONTAINS(@parcelIds, c.tracking_id)`,
        parameters: [
          {
            name: '@parcelIds',
            value: parcelIds,
          },
        ],
      });

      if (!parcels?.length) {
        logError(`No parcel found for job ${jobId}`, this.createManifestDocument.name);
        return res.status(404).json({
          success: false,
          message: `No parcel found for job ${jobId}`,
        });
      }

      const merchantAccountNumbers = [
        ...new Set(parcels.map((parcel) => CrtCounterEncrypt(parcel.merchant_account_number))),
      ];

      const merchants = await this.merchantService.getByQuery({
        query: `SELECT 
          c.id, 
          c.merchant_name, 
          c.merchant_account_number, 
          c.street, 
          c.city, 
          c.state, 
          c.postal_code, 
          c.country, 
          c.phone_number 
          FROM c
          WHERE ARRAY_CONTAINS(@merchantAccountNumbers, c.merchant_account_number)
          `,
        parameters: [
          {
            name: '@merchantAccountNumbers',
            value: merchantAccountNumbers,
          },
        ],
      });

      if (!merchants?.length) {
        logError(`No merchant found for job ${jobId}`, this.createManifestDocument.name);
        return res.status(404).json({
          success: false,
          message: `No merchant found for job ${jobId}`,
        });
      }

      const buffer = await this.fmdService.generateFmdManifestDocument(parcels, merchants, jobId);

      return res.status(200).json({
        success: true,
        data: buffer,
      });
    } catch (e) {
      logError(
        `Unable to create FMD Manifest Document for job ${jobId}: ${e.message}`,
        this.createManifestDocument.name,
      );
      return res.status(500).json({
        success: false,
        message: e.message,
      });
    }
  }
}
