import { AppRequest } from '../../auth/auth.request';
import { FmdCompanyDaoService } from '../../db/fmd/fmd-company.dao.service';
import { FmdDriverDaoService } from '../../db/fmd/fmd-driver.dao.service';
import { FmdJobDaoService } from '../../db/fmd/fmd-job.dao.service';
import { ManifestItemDaoService } from '../../db/manifest-item';
import { ManifestItemArchiveDaoService } from '../../db/manifest-item-archive';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { CountryISOService } from '../../shared/CountryISOService';
import { MerchantService } from '../merchant/merchant.service';
import { ParcelService } from '../parcel/parcel.service';
import { FmdController } from './fmd.controller';
import { CreateManifestDocumentDto } from './fmd.dto';
import { FmdService } from './fmd.service';
import axios from 'axios';

jest.mock('axios');

describe('ParcelController', () => {
  let fmdController: FmdController;
  let parcelService: ParcelService;
  let manifestItemDaoService: ManifestItemDaoService;
  let manifestItemArchiveDaoService: ManifestItemArchiveDaoService;
  let fmdCompanyDaoService: FmdCompanyDaoService;
  let fmdService: FmdService;
  let merchantDaoService: MerchantDaoService;
  let merchantService: MerchantService;
  let request: AppRequest;
  let countryISOService: CountryISOService;
  let fmdJobDaoService: FmdJobDaoService;
  let fmdDriverDaoService: FmdDriverDaoService;

  beforeEach(async () => {
    fmdCompanyDaoService = new FmdCompanyDaoService();
    merchantDaoService = new MerchantDaoService();
    manifestItemArchiveDaoService = new ManifestItemArchiveDaoService();
    manifestItemDaoService = new ManifestItemDaoService(manifestItemArchiveDaoService);
    countryISOService = new CountryISOService();
    fmdJobDaoService = new FmdJobDaoService();
    merchantService = new MerchantService(merchantDaoService);
    parcelService = new ParcelService(manifestItemDaoService);
    fmdDriverDaoService = new FmdDriverDaoService();

    // Mock request object with decoded token
    request = {
      decoded: { emails: ['<EMAIL>'] },
    } as AppRequest;

    fmdService = new FmdService(
      request,
      fmdCompanyDaoService,
      merchantDaoService,
      manifestItemDaoService,
      countryISOService,
      fmdJobDaoService,
      fmdDriverDaoService,
      parcelService,
    );

    fmdController = new FmdController(fmdCompanyDaoService, fmdService, merchantService, parcelService);
  });

  describe('createFmdManifestDocument', () => {
    const createFmdManifestDocumentDto: CreateManifestDocumentDto = {
      jobId: 'a8ea66a6-7e49-4de6-a099-9ecd667046ff',
      parcelIds: ['12f56d77-4b65-4312-b094-911d09d5331d'],
    };
    const expectedParcels = [
      {
        id: 'd3f8942d-638c-41d9-8be9-fe0a02b646e2',
        tracking_id: '',
        tracking_no: '',
        weight: 2.5,
        weight_unit: 'kg',
        merchant_name: 'iherb',
        merchant_account_number: 'US000001',
        origin: 'SIN',
        destination_group: 'DG_BXC_AKL',
        operation_hub: '',
        merchant_declared_currency: 'USD',
        recipient_first_name: 'LongMia',
        recipient_last_name: 'PauloTan',
        recipient_addressline1: 'Conguerisus',
        recipient_addressline2: 'Consequategestas',
        recipient_addressline3: 'Euismodcommodo',
        phone_country_code: '',
        phone: '**********',
        city_suburb: 'Penatibusadipiscing',
        state: 'Ullamcorperquam',
        postcode: '',
        country: 'Sapienproin',
        item: [
          {
            hs_code: '',
            total_declared_value: 9.95,
            quantity: 1,
            description: '',
            subtotal_weight: 1,
          },
        ],
      },
    ];
    const expectedMerchants = [
      {
        id: 'e15d1eec-2816-4eef-87fc-b7a7a0c4f273',
        merchant_name: 'iherb',
        merchant_account_number: 'US000001',
        street: 'Sociosquvel',
        city: 'Consecteturtempor',
        state: 'Magnaplacerat',
        postal_code: 'Habitantest',
        country: 'Temporlacus',
        phone_number: '**********',
      },
    ];

    it('should return false when there is unexpected exception', async () => {
      // Arrange
      const response: any = {
        status: jest.fn().mockReturnValue({ json: jest.fn().mockReturnValue({ success: false }) }),
      };

      // Mock authorization to pass
      jest.spyOn(fmdService, 'validateJobAccess').mockResolvedValue(undefined);
      jest.spyOn(parcelService, 'getByQuery').mockRejectedValueOnce(new Error('error'));

      // Act
      const res = await fmdController.createManifestDocument(createFmdManifestDocumentDto, response);

      // Assert
      expect(res).toEqual(
        expect.objectContaining({
          success: false,
        }),
      );
    });

    it('should return false when no parcel found', async () => {
      // Arrange
      const response: any = {
        status: jest.fn().mockReturnValue({ json: jest.fn().mockReturnValue({ success: false }) }),
      };

      // Mock authorization to pass
      jest.spyOn(fmdService, 'validateJobAccess').mockResolvedValue(undefined);
      jest.spyOn(parcelService, 'getByQuery').mockResolvedValueOnce([]);

      // Act
      const res = await fmdController.createManifestDocument(createFmdManifestDocumentDto, response);

      // Assert
      expect(res).toEqual(
        expect.objectContaining({
          success: false,
        }),
      );
    });

    it('should return false when no merchant found', async () => {
      // Arrange
      const response: any = {
        status: jest.fn().mockReturnValue({ json: jest.fn().mockReturnValue({ success: false }) }),
      };

      // Mock authorization to pass
      jest.spyOn(fmdService, 'validateJobAccess').mockResolvedValue(undefined);
      jest.spyOn(parcelService, 'getByQuery').mockResolvedValueOnce(expectedParcels);
      jest.spyOn(merchantService, 'getByQuery').mockResolvedValueOnce([]);

      // Act
      const res = await fmdController.createManifestDocument(createFmdManifestDocumentDto, response);

      // Assert
      expect(res).toEqual(
        expect.objectContaining({
          success: false,
        }),
      );
    });

    it('should create FMD Manifest Document successfully', async () => {
      // Arrange
      const response: any = {
        status: jest.fn().mockReturnValue({ json: jest.fn().mockReturnValue({ success: true }) }),
      };

      // Mock authorization to pass
      jest.spyOn(fmdService, 'validateJobAccess').mockResolvedValue(undefined);
      jest.spyOn(parcelService, 'getByQuery').mockResolvedValueOnce(expectedParcels);
      jest.spyOn(merchantService, 'getByQuery').mockResolvedValueOnce(expectedMerchants);
      axios.post = jest.fn().mockResolvedValueOnce({
        data: Buffer.from('hello world'),
      });

      // Act
      const res = await fmdController.createManifestDocument(createFmdManifestDocumentDto, response);

      // Assert
      expect(res).toEqual(
        expect.objectContaining({
          success: true,
        }),
      );
    });
  });
});
