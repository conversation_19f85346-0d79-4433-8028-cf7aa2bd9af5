import { Test, TestingModule } from '@nestjs/testing';
import { Reflector } from '@nestjs/core';
import { ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { FmdAuthorizationGuard } from './fmd-authorization.guard';
import { FmdDriverDaoService } from '../../db/fmd/fmd-driver.dao.service';
import { FmdCompanyDaoService } from '../../db/fmd/fmd-company.dao.service';
import { FmdJobDaoService } from '../../db/fmd/fmd-job.dao.service';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { CountryISOService } from '../../shared/CountryISOService';
import { FmdAuthorizationType } from './fmd-authorization.types';
import { CrtCounterEncrypt, CrtCounterDecrypt } from '../../utils';

jest.mock('../../utils', () => ({
  CrtCounterEncrypt: jest.fn((value) => `encrypted_${value}`),
  CrtCounterDecrypt: jest.fn((value) => value.replace('encrypted_', '')),
}));

describe('FmdAuthorizationGuard', () => {
  let guard: FmdAuthorizationGuard;
  let reflector: Reflector;
  let fmdDriverDaoService: jest.Mocked<FmdDriverDaoService>;
  let fmdCompanyDaoService: jest.Mocked<FmdCompanyDaoService>;
  let fmdJobDaoService: jest.Mocked<FmdJobDaoService>;
  let merchantDaoService: jest.Mocked<MerchantDaoService>;
  let countryIsoService: jest.Mocked<CountryISOService>;

  const mockExecutionContext = {
    getHandler: jest.fn(),
    switchToHttp: jest.fn(() => ({
      getRequest: jest.fn(),
    })),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FmdAuthorizationGuard,
        {
          provide: Reflector,
          useValue: { get: jest.fn() },
        },
        {
          provide: FmdDriverDaoService,
          useValue: { getDriver: jest.fn() },
        },
        {
          provide: FmdCompanyDaoService,
          useValue: { getItem: jest.fn() },
        },
        {
          provide: FmdJobDaoService,
          useValue: { getItem: jest.fn() },
        },
        {
          provide: MerchantDaoService,
          useValue: { find: jest.fn() },
        },
        {
          provide: CountryISOService,
          useValue: { findCountry: jest.fn() },
        },
      ],
    }).compile();

    guard = module.get<FmdAuthorizationGuard>(FmdAuthorizationGuard);
    reflector = module.get<Reflector>(Reflector);
    fmdDriverDaoService = module.get(FmdDriverDaoService);
    fmdCompanyDaoService = module.get(FmdCompanyDaoService);
    fmdJobDaoService = module.get(FmdJobDaoService);
    merchantDaoService = module.get(MerchantDaoService);
    countryIsoService = module.get(CountryISOService);
  });

  describe('canActivate', () => {
    it('should allow access when no authorization type is specified', async () => {
      reflector.get = jest.fn().mockReturnValue(undefined);

      const result = await guard.canActivate(mockExecutionContext as any);

      expect(result).toBe(true);
    });

    it('should throw UnauthorizedException when user is not authenticated', async () => {
      reflector.get = jest.fn().mockReturnValue(FmdAuthorizationType.COMPANY_RESOURCE);
      mockExecutionContext.switchToHttp().getRequest = jest.fn().mockReturnValue({
        decoded: null,
      });

      await expect(guard.canActivate(mockExecutionContext as any)).rejects.toThrow(UnauthorizedException);
    });

    it('should validate company access correctly', async () => {
      const mockRequest = {
        decoded: { emails: ['<EMAIL>'] },
        params: { id: 'company123' },
      };

      reflector.get = jest.fn().mockReturnValue(FmdAuthorizationType.COMPANY_RESOURCE);
      mockExecutionContext.switchToHttp().getRequest = jest.fn().mockReturnValue(mockRequest);

      fmdDriverDaoService.getDriver.mockResolvedValue({
        id: 'driver1',
        companyId: 'company123',
        email: '<EMAIL>',
        name: 'Test Driver',
      });

      fmdCompanyDaoService.getItem.mockResolvedValue({
        id: 'company123',
        company: 'Test Company',
        country: 'Singapore',
        type: 'company',
      });

      const result = await guard.canActivate(mockExecutionContext as any);

      expect(result).toBe(true);
    });

    it('should deny access to different company resources', async () => {
      const mockRequest = {
        decoded: { emails: ['<EMAIL>'] },
        params: { id: 'company456' },
      };

      reflector.get = jest.fn().mockReturnValue(FmdAuthorizationType.COMPANY_RESOURCE);
      mockExecutionContext.switchToHttp().getRequest = jest.fn().mockReturnValue(mockRequest);

      fmdDriverDaoService.getDriver.mockResolvedValue({
        id: 'driver1',
        companyId: 'company123',
        email: '<EMAIL>',
        name: 'Test Driver',
      });

      fmdCompanyDaoService.getItem.mockResolvedValue({
        id: 'company123',
        company: 'Test Company',
        country: 'Singapore',
        type: 'company',
      });

      await expect(guard.canActivate(mockExecutionContext as any)).rejects.toThrow(ForbiddenException);
    });

    it('should validate merchant access from same country', async () => {
      const mockRequest = {
        decoded: { emails: ['<EMAIL>'] },
        params: { merchantAccountNo: 'MERCHANT123' },
      };

      reflector.get = jest.fn().mockReturnValue(FmdAuthorizationType.MERCHANT_RESOURCE);
      mockExecutionContext.switchToHttp().getRequest = jest.fn().mockReturnValue(mockRequest);

      fmdDriverDaoService.getDriver.mockResolvedValue({
        id: 'driver1',
        companyId: 'company123',
        email: '<EMAIL>',
        name: 'Test Driver',
      });

      fmdCompanyDaoService.getItem.mockResolvedValue({
        id: 'company123',
        company: 'Test Company',
        country: 'Singapore',
        type: 'company',
      });

      merchantDaoService.find.mockResolvedValue([{
        id: 'merchant1',
        merchant_account_number: 'encrypted_MERCHANT123',
        country: 'encrypted_Singapore',
      }]);

      countryIsoService.findCountry.mockReturnValue({
        codeAlpha2: 'SG',
        codeAlpha3: 'SGP',
        name: 'Singapore',
      });

      const result = await guard.canActivate(mockExecutionContext as any);

      expect(result).toBe(true);
    });

    it('should deny access to merchant from different country', async () => {
      const mockRequest = {
        decoded: { emails: ['<EMAIL>'] },
        params: { merchantAccountNo: 'MERCHANT123' },
      };

      reflector.get = jest.fn().mockReturnValue(FmdAuthorizationType.MERCHANT_RESOURCE);
      mockExecutionContext.switchToHttp().getRequest = jest.fn().mockReturnValue(mockRequest);

      fmdDriverDaoService.getDriver.mockResolvedValue({
        id: 'driver1',
        companyId: 'company123',
        email: '<EMAIL>',
        name: 'Test Driver',
      });

      fmdCompanyDaoService.getItem.mockResolvedValue({
        id: 'company123',
        company: 'Test Company',
        country: 'Singapore',
        type: 'company',
      });

      merchantDaoService.find.mockResolvedValue([{
        id: 'merchant1',
        merchant_account_number: 'encrypted_MERCHANT123',
        country: 'encrypted_Korea',
      }]);

      countryIsoService.findCountry
        .mockReturnValueOnce({ codeAlpha2: 'SG', codeAlpha3: 'SGP', name: 'Singapore' })
        .mockReturnValueOnce({ codeAlpha2: 'KR', codeAlpha3: 'KOR', name: 'Korea' });

      await expect(guard.canActivate(mockExecutionContext as any)).rejects.toThrow(ForbiddenException);
    });
  });
});
