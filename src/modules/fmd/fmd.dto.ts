export interface CreateManifestDocumentDto {
  jobId: string;
  parcelIds: string[];
}

export interface FMDManifestDocumentItemI {
  sn: number;
  hawb: string;
  pieces: number;
  origin_destination: string;
  weight: string;
  goods_description: string;
  currency: string;
  value: string;
  hs_codes: string;
  shipper_name: string;
  shipper_address: string;
  shipper_contact: string;
  consignee_name: string;
  consignee_address: string;
  consignee_contact: string;
}

export interface FMDManifestDocumentPayloadI {
  jobId: string;
  data: FMDManifestDocumentItemI[];
}
