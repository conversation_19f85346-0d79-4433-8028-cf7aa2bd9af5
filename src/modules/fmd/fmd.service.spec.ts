import { Test, TestingModule } from '@nestjs/testing';
import { FmdService } from './fmd.service';
import { FmdCompanyDaoService } from '../../db/fmd/fmd-company.dao.service';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { ManifestItemDaoService } from '../../db/manifest-item';
import { CountryISOService } from '../../shared/CountryISOService';
import { FmdJobDaoService } from '../../db/fmd/fmd-job.dao.service';
import { FmdDriverDaoService } from '../../db/fmd/fmd-driver.dao.service';
import { ParcelService } from '../parcel/parcel.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { AppRequest } from '../../auth/auth.request';

jest.mock('axios');
jest.mock('../../utils', () => {
  return {
    CrtCounterEncrypt: jest.fn().mockImplementation((value) => value),
    CrtCounterDecrypt: jest.fn().mockImplementation((value) => value),
  };
});

describe('FmdService', () => {
  let service: FmdService;
  let fmdCompanyDaoService: FmdCompanyDaoService;
  let merchantDaoService: MerchantDaoService;
  let manifestItemDaoService: ManifestItemDaoService;
  let fmdJobDaoService: FmdJobDaoService;
  let fmdDriverDaoService: FmdDriverDaoService;
  let countryIsoService: CountryISOService;

  const mockRequest = {
    decoded: { emails: ['<EMAIL>'] },
  } as AppRequest;
  const mockJob = { id: 'job-id', parcel_list: [] };
  const mockParcel = { id: 'parcel-id', merchant_account_number: 'merchant123', item: [] };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FmdService,
        { provide: FmdCompanyDaoService, useValue: { getItem: jest.fn() } },
        { provide: MerchantDaoService, useValue: { getUniqueMerchantsByStreetForCountry: jest.fn() } },
        {
          provide: ManifestItemDaoService,
          useValue: {
            getParcelsOfMerchant: jest.fn(),
            getParcelByTrackingNoOrId: jest.fn().mockResolvedValue(mockParcel),
          },
        },
        { provide: CountryISOService, useValue: { findCountry: jest.fn() } },
        {
          provide: FmdJobDaoService,
          useValue: {
            getPickedUpJobByUser: jest.fn(),
            createPickedUpJob: jest.fn(),
            getItem: jest.fn().mockResolvedValue(mockJob),
          },
        },
        { provide: FmdDriverDaoService, useValue: { getDriver: jest.fn() } },
        { provide: ParcelService, useValue: { validateForScan: jest.fn() } },
        { provide: REQUEST, useValue: mockRequest },
      ],
    }).compile();

    service = await module.resolve<FmdService>(FmdService);
    fmdCompanyDaoService = module.get<FmdCompanyDaoService>(FmdCompanyDaoService);
    merchantDaoService = module.get<MerchantDaoService>(MerchantDaoService);
    manifestItemDaoService = module.get<ManifestItemDaoService>(ManifestItemDaoService);
    fmdJobDaoService = module.get<FmdJobDaoService>(FmdJobDaoService);
    fmdDriverDaoService = module.get<FmdDriverDaoService>(FmdDriverDaoService);
    countryIsoService = module.get<CountryISOService>(CountryISOService);
    jest.spyOn(fmdJobDaoService, 'getItem').mockImplementation(async () => mockJob as any);
    jest.spyOn(fmdCompanyDaoService, 'getItem').mockImplementation(
      async (id: string) =>
        ({
          id,
          country: 'US',
        } as any),
    );
    jest.spyOn(merchantDaoService, 'getUniqueMerchantsByStreetForCountry').mockImplementation(
      async (id: string) =>
        [
          {
            id,
            street: 'encryptedStreet',
            merchant_name: 'encryptedName',
            merchant_account_number: 'encryptedAccount',
          },
        ] as any,
    );
  });

  describe('getMerchantPickupAddressListForCompany', () => {
    it('should return decrypted merchant list for a given company', async () => {
      countryIsoService.findCountry = jest.fn().mockReturnValue('US');

      // Mock authorization methods
      jest.spyOn(service as any, 'validateCompanyAccess').mockResolvedValue(undefined);

      const result = await service.getMerchantPickupAddressListForCompany('company-id');

      expect(fmdCompanyDaoService.getItem).toHaveBeenCalledWith('company-id');
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            street: expect.any(String),
            merchant_name: expect.any(String),
            merchant_account_number: expect.any(String),
          }),
        ]),
      );
    });

    it('should throw an HttpException on error', async () => {
      // Mock authorization to pass, then let the actual service method fail
      jest.spyOn(service as any, 'validateCompanyAccess').mockResolvedValue(undefined);
      jest.spyOn(fmdCompanyDaoService, 'getItem').mockRejectedValue(new Error('error'));

      await expect(service.getMerchantPickupAddressListForCompany('company-id')).rejects.toThrow(
        new HttpException('error', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  describe('getPickupParcelsForMerchant', () => {
    it('should return parcels for a given merchant account number', async () => {
      const mockParcels = [{ id: 'parcel1' }];
      jest.spyOn(manifestItemDaoService, 'getParcelsOfMerchant').mockResolvedValue(mockParcels as any);

      // Mock authorization methods
      jest.spyOn(service as any, 'validateMerchantAccess').mockResolvedValue(undefined);

      const result = await service.getPickupParcelsForMerchant('merchant-account-no');

      expect(manifestItemDaoService.getParcelsOfMerchant).toHaveBeenCalledWith('merchant-account-no');
      expect(result).toEqual(mockParcels);
    });

    it('should throw an HttpException on error', async () => {
      // Mock authorization to pass, then let the actual service method fail
      jest.spyOn(service as any, 'validateMerchantAccess').mockResolvedValue(undefined);
      jest.spyOn(manifestItemDaoService, 'getParcelsOfMerchant').mockRejectedValue(new Error('some error'));

      await expect(service.getPickupParcelsForMerchant('merchant-account-no')).rejects.toThrow(
        new HttpException('some error', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  describe('getPickedUpJob', () => {
    it('should return a list of picked-up jobs with decrypted merchant and driver data', async () => {
      jest.spyOn(fmdJobDaoService, 'getPickedUpJobByUser').mockResolvedValue([
        {
          merchant_no: 'encryptedMerchantNo',
          driver_email: 'encryptedDriverEmail',
        },
      ] as any);
      jest.spyOn(fmdDriverDaoService, 'getDriver').mockResolvedValue({
        name: 'Driver Name',
      } as any);
      const result = await service.getPickedUpJob();

      // Verify the job details have been decrypted and driver details are included
      expect(result).toEqual([
        expect.objectContaining({
          merchant_no: 'encryptedMerchantNo',
          driver_name: 'Driver Name',
          driver_email: 'encryptedDriverEmail',
        }),
      ]);
    });
    it('should throw HttpException if there is an error', async () => {
      jest.spyOn(fmdJobDaoService, 'getPickedUpJobByUser').mockRejectedValue(new Error('Database error'));
      await expect(service.getPickedUpJob()).rejects.toThrow(HttpException);
    });
    it('should log an error and skip job if driver not found', async () => {
      // Mock getDriver to return undefined to simulate a missing driver
      jest.spyOn(fmdJobDaoService, 'getPickedUpJobByUser').mockResolvedValue([{} as any]);
      jest.spyOn(fmdDriverDaoService, 'getDriver').mockResolvedValue(undefined);

      const result = await service.getPickedUpJob();

      // Ensure missing driver log is recorded
      expect(result).toEqual([
        expect.objectContaining({
          merchant_no: undefined,
        }),
      ]);
    });
  });

  describe('createPickedUpJob', () => {
    it('should create a picked-up job with encrypted driver and merchant details', async () => {
      jest.spyOn(fmdJobDaoService, 'createPickedUpJob').mockResolvedValue({
        id: 'job-id',
        driver_email: 'encryptedDriverEmail',
        merchant_account_no: 'encryptedMerchantAccountNo',
      } as any);

      // Mock authorization methods
      jest.spyOn(service as any, 'validateMerchantAccess').mockResolvedValue(undefined);

      const merchantAccountNo = 'merchant123';

      const result = await service.createJob(merchantAccountNo);

      // Check if returned job is as expected
      expect(result).toEqual({
        id: 'job-id',
        driver_email: 'encryptedDriverEmail',
        merchant_account_no: 'encryptedMerchantAccountNo',
      });
    });

    it('should log an error and throw HttpException if job creation fails', async () => {
      // Mock authorization to pass, then let the actual service method fail
      jest.spyOn(service as any, 'validateMerchantAccess').mockResolvedValue(undefined);
      // Simulate an error during job creation
      jest.spyOn(fmdJobDaoService, 'createPickedUpJob').mockRejectedValue(new Error('Database error'));

      await expect(service.createJob('merchant123')).rejects.toThrow(HttpException);
    });

    it('should throw an error and throw HttpException if job creation is empty', async () => {
      // Mock authorization to pass, then let the actual service method fail
      jest.spyOn(service as any, 'validateMerchantAccess').mockResolvedValue(undefined);
      // Simulate an error during job creation
      jest.spyOn(fmdJobDaoService, 'createPickedUpJob').mockResolvedValue(undefined);

      await expect(service.createJob('merchant123')).rejects.toThrow(HttpException);
    });
  });

  describe('scanParcel', () => {
    it('should log error and throw HttpException if parcel status update fails', async () => {
      // Mock authorization methods to pass
      jest.spyOn(service as any, 'validateJobAccess').mockResolvedValue(undefined);
      jest.spyOn(service as any, 'validateMerchantAccess').mockResolvedValue(undefined);

      // Mock failure in manifest item update
      manifestItemDaoService.patch = jest.fn().mockResolvedValue(undefined);

      const payload = {
        jobId: 'job-id',
        inMerchantPickupList: true,
        parcelBarcode: 'barcode123',
        merchantAccountNo: 'merchant123',
      };

      await expect(service.scanParcel(payload)).rejects.toThrow(HttpException);
    });
  });
});
