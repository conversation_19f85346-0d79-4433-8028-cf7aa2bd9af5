import { HttpException, HttpStatus, Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { ParcelService } from '../parcel/parcel.service';

import { CrtCounterDecrypt, CrtCounterEncrypt, JobType, ParcelStatus, ValidateResult } from '../../utils';

import { Resource } from '@azure/cosmos';
import { AppRequest } from '../../auth/auth.request';
import { FmdCompanyDaoService } from '../../db/fmd/fmd-company.dao.service';
import { FmdJob, FmdScanParcelPayload } from '../../db/fmd/fmd-dao.model';
import { FmdDriverDaoService } from '../../db/fmd/fmd-driver.dao.service';
import { FmdJobDaoService } from '../../db/fmd/fmd-job.dao.service';
import { DbParcel, ManifestItemDaoService } from '../../db/manifest-item';
import { Merchant } from '../../db/merchant/merchant-dao.model';
import { MerchantDaoService } from '../../db/merchant/merchant-dao.service';
import { CountryISOService } from '../../shared/CountryISOService';
import { MerchantWebHookApi } from '../../shared/MerchantWebHookApi';
import { StatusMappingService } from '../../shared/StatusMappingService';
import { logError, logInfo } from '../../utils/LogUtils';
import { FMDManifestDocumentPayloadI } from './fmd.dto';
import { AppConfig } from '../../resources';
import axios from 'axios';

@Injectable({ scope: Scope.REQUEST })
export class FmdService {
  constructor(
    @Inject(REQUEST) private readonly request: AppRequest,
    private fmdCompanyDaoService: FmdCompanyDaoService,
    private merchantDaoService: MerchantDaoService,
    private manifestItemDaoService: ManifestItemDaoService,
    private countryIsoService: CountryISOService,
    private fmdJobDaoService: FmdJobDaoService,
    private fmdDriverDaoService: FmdDriverDaoService,
    private parcelService: ParcelService,
  ) {}

  async getMerchantPickupAddressListForCompany(companyId: string) {
    try {
      const company = await this.fmdCompanyDaoService.getItem(companyId);
      const countryCode = this.countryIsoService.findCountry(company.country);
      const merchants = await this.merchantDaoService.getUniqueMerchantsByStreetForCountry(countryCode.codeAlpha2);

      merchants.forEach((merchant) => {
        merchant.street = CrtCounterDecrypt(merchant.street);
        merchant.merchant_name = CrtCounterDecrypt(merchant.merchant_name);
        merchant.merchant_account_number = CrtCounterDecrypt(merchant.merchant_account_number);
      });

      return merchants;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getPickupParcelsForMerchant(merchantAccountNo: string) {
    try {
      return await this.manifestItemDaoService.getParcelsOfMerchant(merchantAccountNo);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getPickedUpJob() {
    try {
      const pickedUpJob = await this.fmdJobDaoService.getPickedUpJobByUser(
        CrtCounterEncrypt(this.request.decoded.emails[0]),
      );
      for (const job of pickedUpJob) {
        job.merchant_no = CrtCounterDecrypt(job.merchant_no);
        const driver = await this.fmdDriverDaoService.getDriver(CrtCounterDecrypt(job.driver_email));
        if (!driver) {
          logError('Can not get driver', 'getPickedUpJob', job.driver_email);
          continue;
        }
        job.driver_name = driver.name;
      }
      return pickedUpJob;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async generateFmdManifestDocument(parcels: DbParcel[], merchants: Merchant[], jobId: string): Promise<Buffer> {
    const details = parcels.map((p, i) => {
      const m = merchants.find((merchant) => merchant.merchant_account_number === p.merchant_account_number);
      return {
        sn: i + 1,
        hawb: p.tracking_no || '',
        pieces: p.item?.length || 0,
        origin_destination: [p.origin || '-', p.destination_group.split('_')[2] || '-'].join('/'),
        weight: String(p.weight) || '',
        goods_description: p.item?.length
          ? p.item
              .map((t) => t.description)
              .filter(Boolean)
              .join(', ')
          : '',
        currency: p.merchant_declared_currency || '',
        value: p.item?.length
          ? p.item.reduce((prev, curr) => prev + Number(curr.total_declared_value), 0).toFixed(2)
          : '0',
        hs_codes: p.item?.length ? p.item.map((t) => t.hs_code || '-').join(',\n') : '',
        shipper_name: m?.merchant_name || '',
        shipper_address: [m?.street, m?.city, m?.postal_code, m?.country].filter(Boolean).join(', '),
        shipper_contact: m?.phone_number || '',
        consignee_name: [p.recipient_first_name, p.recipient_last_name].filter(Boolean).join(', '),
        consignee_address: [
          p.recipient_addressline1,
          p.recipient_addressline2,
          p.recipient_addressline3,
          p.city_suburb,
          p.state,
          p.postcode,
          p.country,
        ]
          .filter(Boolean)
          .join(', '),
        consignee_contact: [p.phone_country_code ? `(${p.phone_country_code})` : null, p.phone]
          .filter(Boolean)
          .join(' '),
      };
    });

    const url = `${AppConfig.pdfFuncUrl}/fmd-manifest-documents`;

    const payload: FMDManifestDocumentPayloadI = {
      jobId,
      data: details,
    };

    const { data } = await axios.post(url, payload, { responseType: 'arraybuffer' });

    return Buffer.from(data);
  }

  async scanParcel(payload: FmdScanParcelPayload) {
    const { jobId, inMerchantPickupList, parcelBarcode, merchantAccountNo } = payload;
    try {
      const existedJob = await this.fmdJobDaoService.getItem(jobId, JobType.JOB);
      const parcel = await this.manifestItemDaoService.getParcelByTrackingNoOrId(parcelBarcode);

      // validate if it not in the pickup list of merchant
      if (!inMerchantPickupList) {
        const { message, data } = await this.parcelService.validateForScan(parcel, merchantAccountNo);
        if (message !== ValidateResult.PARCEL_ACCEPTED) {
          return {
            success: false,
            message,
            data,
          };
        }
      }
      //check parcel is scanned or not
      if (existedJob.parcel_list.includes(parcel.id)) {
        return {
          success: false,
          message: ValidateResult.PARCEL_SCANNED_ALREADY,
        };
      }
      //update parcel status in manifest item
      const driverEmail = this.request.decoded.emails[0];
      const parcelPickupStatus = StatusMappingService.getManifestStatus(ParcelStatus.PICKED_UP);

      const date = new Date();
      const updatedParcel = await this.manifestItemDaoService.patch({
        id: parcel.id,
        latest_tracking_status: parcelPickupStatus,
        tracking_status: [{ status: parcelPickupStatus, date, picked_up_by: CrtCounterEncrypt(driverEmail) }],
      });
      if (!updatedParcel) {
        throw new HttpException('Update parcel pickup status failed', HttpStatus.FAILED_DEPENDENCY);
      }
      // update parcel status to FIN-API blockchain and webhook service for succeeded parcel
      MerchantWebHookApi.updateStatus(parcel, ParcelStatus.PICKED_UP, date);
      //update parcel list of job db
      const updatedJob = await this.fmdJobDaoService.patch({
        id: jobId,
        partitionKey: JobType.JOB,
        parcel_list: [parcel.id],
      });
      logInfo('Scan parcel', 'scanParcel', `Driver's email: ${driverEmail}`, 'Succeeded scan parcel to job');

      return {
        success: true,
        data: updatedJob,
      };
    } catch (error) {
      logError(`Scan parcel ${parcelBarcode} failed`, 'scanParcel', error);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async createJob(merchantAccountNo: string) {
    try {
      const driverEmail = this.request.decoded.emails[0];

      // create pickedUp job
      const job: FmdJob & Resource = await this.fmdJobDaoService.createPickedUpJob(
        CrtCounterEncrypt(driverEmail),
        CrtCounterEncrypt(merchantAccountNo),
      );

      if (!job) {
        throw new HttpException('Unable to create a new Pickuped Job', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      logInfo('Create new job', 'createJob', `driver's email: ${driverEmail}\n`, 'Succeeded create new job');

      return job;
    } catch (error) {
      logError(`Create new job of merchant ${merchantAccountNo} failed`, 'createJob', error);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
