import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../../auth/auth.guard';
import { OperationHubsService } from './operation-hubs.service';

@UseGuards(AuthGuard)
@Controller('operation-hubs')
export class OperationHubsController {
  constructor(private opsHubService: OperationHubsService) {}

  @Get('by-country/:country')
  async getByCountry(@Param() params): Promise<any> {
    return this.opsHubService.getByCountry(params.country);
  }
}
