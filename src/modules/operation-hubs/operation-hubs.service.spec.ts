import { Test, TestingModule } from '@nestjs/testing';
import { OperationHubsService } from './operation-hubs.service';
import { OperationHubDAOService } from '../../db/operation-hub/operation-hub-dao.service';

describe('OperationHubsService', () => {
  let operationHubsService: OperationHubsService;
  let opsHubDaoClient: OperationHubDAOService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OperationHubsService,
        {
          provide: OperationHubDAOService,
          useValue: { getByCountry: jest.fn() },
        },
      ],
    }).compile();

    operationHubsService = module.get<OperationHubsService>(OperationHubsService);
    opsHubDaoClient = module.get<OperationHubDAOService>(OperationHubDAOService);
  });

  it('should return operation hubs for a given country', async () => {
    // Arrange
    const country = 'US';
    const mockHubs = [
      { id: 'hub1', name: 'New York Hub', country: 'US' },
      { id: 'hub2', name: 'California Hub', country: 'US' },
    ];
    (opsHubDaoClient.getByCountry as jest.Mock).mockResolvedValue(mockHubs);

    // Act
    const result = await operationHubsService.getByCountry(country);

    // Assert
    expect(opsHubDaoClient.getByCountry).toHaveBeenCalledWith(country); // Check correct parameter
    expect(result).toEqual(mockHubs); // Ensure the result matches the mocked data
  });

  it('should return an empty list if no operation hubs are found', async () => {
    // Arrange
    const country = 'CA';
    (opsHubDaoClient.getByCountry as jest.Mock).mockResolvedValue([]); // No hubs found

    // Act
    const result = await operationHubsService.getByCountry(country);

    // Assert
    expect(result).toEqual([]); // Should return an empty list if no hubs are found
  });
});
