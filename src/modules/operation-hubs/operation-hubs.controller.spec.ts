import { OperationHubsController } from './operation-hubs.controller';
import { OperationHubsService } from './operation-hubs.service';
import { OperationHubDAOService } from '../../db/operation-hub/operation-hub-dao.service';

describe('OperationHubsController', () => {
  let operationHubController: OperationHubsController;
  let operationHubService: OperationHubsService;

  beforeEach(async () => {
    operationHubService = new OperationHubsService(new OperationHubDAOService());
    operationHubController = new OperationHubsController(operationHubService);
  });

  describe('root', () => {
    it('should return list opHub by country', async () => {
      const testResult = [{ country: 'Singapore' }];
      operationHubController.getByCountry = jest.fn().mockResolvedValue(testResult);
      const result = await operationHubController.getByCountry('Singapore');
      expect(result).toStrictEqual(result);
    });
  });
});
