import { Module } from '@nestjs/common';

import { AuthModule } from 'src/auth/auth.module';
import { OperationHubDAOService } from '../../db/operation-hub/operation-hub-dao.service';
import { OperationHubsController } from './operation-hubs.controller';
import { OperationHubsService } from './operation-hubs.service';

@Module({
  imports: [AuthModule],
  controllers: [OperationHubsController],
  providers: [OperationHubsService, OperationHubDAOService],
})
export class OperationHubsModule {}
