import { SqlQuerySpec } from '@azure/cosmos';
import { MerchantDaoService } from './merchant-dao.service';
import { CrtCounterEncrypt } from '../../../src/utils';

jest.mock('../../../src/utils', () => ({
  CrtCounterEncrypt: jest.fn(),
}));

describe('MerchantDaoService', () => {
  let merchantDaoService: MerchantDaoService;
  const mockEncryptedCountryId = 'encryptedCountryId';

  beforeEach(() => {
    merchantDaoService = new MerchantDaoService();
    merchantDaoService.find = jest.fn(); // Mock the inherited 'find' method
    (CrtCounterEncrypt as jest.Mock).mockReturnValue(mockEncryptedCountryId); // Mock encryption
  });

  it('should call find with correct query and parameters', async () => {
    // Arrange
    const countryId = 'US';
    const mockResult = [
      {
        id: '1',
        street: '123 Main St',
        postal_code: '12345',
        merchant_name: 'Merchant A',
        merchant_account_number: '12345',
      },
    ];
    (merchantDaoService.find as jest.Mock).mockResolvedValue(mockResult); // Mock response

    // Act
    const result = await merchantDaoService.getUniqueMerchantsByStreetForCountry(countryId);

    // Assert
    const expectedQuerySpec: SqlQuerySpec = {
      query: `SELECT c.id, c.street, c.postal_code, c.merchant_name, c.merchant_account_number
                FROM c
                Where c.country = @countryId`,
      parameters: [
        {
          name: '@countryId',
          value: mockEncryptedCountryId,
        },
      ],
    };

    expect(CrtCounterEncrypt).toHaveBeenCalledWith(countryId); // Check if encryption was called
    expect(merchantDaoService.find).toHaveBeenCalledWith(expectedQuerySpec); // Check if find was called with correct query
    expect(result).toEqual(mockResult); // Check if the result is as expected
  });

  it('should return an empty array if no merchants are found', async () => {
    // Arrange
    (merchantDaoService.find as jest.Mock).mockResolvedValue([]); // No merchants

    // Act
    const result = await merchantDaoService.getUniqueMerchantsByStreetForCountry('US');

    // Assert
    expect(result).toEqual([]); // Should return an empty array
  });
});
