import { Injectable } from '@nestjs/common';
import { SqlQuerySpec } from '@azure/cosmos';
import { AppConfig } from '../../resources';
import { CrtCounterEncrypt } from '../../utils';
import { BaseService } from '../base.service';
import { Merchant } from './merchant-dao.model';

@Injectable()
export class MerchantDaoService extends BaseService<Merchant> {
  constructor() {
    super(AppConfig.db.merchant);
  }

  /**
   * Getting a list of merchants addresses in unique.
   * @param countryId country id to find merchants from
   * @returns
   */
  async getUniqueMerchantsByStreetForCountry(countryId: string) {
    const encryptedCountry = CrtCounterEncrypt(countryId);

    const querySpec: SqlQuerySpec = {
      query: `SELECT c.id, c.street, c.postal_code, c.merchant_name, c.merchant_account_number
                FROM c
                Where c.country = @countryId`,
      parameters: [
        {
          name: '@countryId',
          value: encryptedCountry,
        },
      ],
    };

    return this.find(querySpec);
  }
}
