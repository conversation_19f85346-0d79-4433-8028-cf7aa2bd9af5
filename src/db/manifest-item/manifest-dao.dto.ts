export interface DbParcelTrackingStatus {
  status: string;
  date: Date;
}

export interface DbParcelItem {
  seq_no?: string | number;
  description?: string;
  quantity?: number;
  total_declared_value?: number;
  SKU?: string;
  subtotal_weight?: string | number;
  hs_code?: string;
}

export interface DbParcelSender {
  sender_first_name: string;
  sender_last_name: string;
  email: string;
  phone: string;
  addressline1: string;
  addressline2: string;
  addressline3: string;
  city_suburb: string;
  state: string;
  postcode: string;
  country: string;
}

export interface DbParcelPayload {
  /** Required. User settable property. Unique name that identifies the item, that is, no two items share the same ID within a database. The id must not exceed 255 characters. */
  //id: string;
  /** System generated property. The resource ID (_rid) is a unique identifier that is also hierarchical per the resource stack on the resource model. It is used internally for placement and navigation of the item resource. */
  //_rid: string;
  /** System generated property. Specifies the last updated timestamp of the resource. The value is a timestamp. */
  //_ts: number;
  /** System generated property. The unique addressable URI for the resource. */
  //_self: string;
  /** System generated property. Represents the resource etag required for optimistic concurrency control. */
  //_etag: string;
  PLS_shipment_booking_reference?: string;
  merchant_batch_order_no?: string;
  merchant_order_no?: string;
  order_date?: string;
  recipient_first_name?: string;
  recipient_last_name?: string;
  recipient_company?: string;
  recipient_addressline1: string;
  recipient_addressline2?: string;
  recipient_addressline3?: string;
  city_suburb?: string;
  state?: string;
  postcode?: string;
  country: string;
  phone?: string;
  phone_country_code?: string;
  email?: string;
  weight?: string | number;
  weight_unit?: string;
  length?: string | number;
  width?: string | number;
  height?: string | number;
  dimensions_unit?: string;
  merchant_declared_currency?: string;
  incoterm?: string;
  battery_type?: string;
  battery_packing?: string;
  contains_other_dg?: string;
  shipping_instruction?: string;
  service_option?: string;
  merchant_drop_off?: string;
  operation_hub?: string;
  merchant_name: string;
  promotion_type?: string;
  insure?: string;
  cod?: string;
  collection_point_name?: string;
  shipment_type?: string;
  pickup_required?: string;
  item?: DbParcelItem[];
  shipment_notification?: string;
  from?: DbParcelSender;
  associated_PLS_shipment?: string;
  shipping_and_insurance_cost?: string;
  own_lable?: string;
  tracking_status?: DbParcelTrackingStatus[];
  tracking_id?: string;
  destination_group?: string;
  origin?: string;
}

export interface DbParcel extends DbParcelPayload {
  id: string;
  tracking_no?: string;
  latest_tracking_status?: string;
  merchant_account_number?: string;
  PLS_batch_no?: string;
}
