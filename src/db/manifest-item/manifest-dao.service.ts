import { Injectable } from '@nestjs/common';
import { BaseService } from '../base.service';
import { AppConfig } from '../../resources';
import { ManifestItemArchiveDaoService } from '../manifest-item-archive';
import { SqlQuerySpec } from '@azure/cosmos';
import { ParcelStatus } from '../../utils/Enum';
import { StatusMappingService } from '../../shared/StatusMappingService';
import { DbParcel } from './manifest-dao.dto';
import { format, subDays } from 'date-fns';
import { CrtCounterEncrypt } from '../../utils';

@Injectable()
export class ManifestItemDaoService extends BaseService<DbParcel> {
  constructor(public manifestItemArchive: ManifestItemArchiveDaoService) {
    super(AppConfig.db.manifestItemColId);
  }

  /**
   * Return parcel by tracking no
   * @param trackingNo
   */
  public async getParcelByTrackingNoOrId(trackingNo: string) {
    const query = {
      query: 'SELECT * FROM c WHERE c.tracking_no = @trackingNo OR c.tracking_id = @trackingNo OR c.id = @trackingNo',
      parameters: [
        {
          name: '@trackingNo',
          value: trackingNo,
        },
      ],
    };
    const parcels = await this.find(query);
    if (parcels.length) {
      return parcels[0];
    }
    console.log('<ManifestService><getParcelByTrackingNoOrId> parcel is not found', trackingNo);
    return null;
  }

  /** Get parcels booked from a list of merchants that have:
   * a booked date that is lower or equal to 60 days
   * and have the status Booked or LMD received. */
  getParcelsOfMerchant(merchantAccountNo: string) {
    const today = format(new Date(), 'yyyy-MM-dd');
    const last60Days = format(subDays(new Date(), 60), 'yyyy-MM-dd');
    merchantAccountNo = CrtCounterEncrypt(merchantAccountNo);

    const latestTrackingStatuses = [ParcelStatus.LMD_RECEIVE_BOOKING, ParcelStatus.BOOKED].map((item) =>
      StatusMappingService.getManifestStatus(item),
    );

    const querySpec: SqlQuerySpec = {
      query: `SELECT c.id, c.tracking_id
              FROM c
              WHERE c.merchant_account_number = @merchant_account_no
                AND ARRAY_CONTAINS(@latest_statuses, c.latest_tracking_status)
                AND c.order_date >= @start_date AND c.order_date <= @today`,
      parameters: [
        {
          name: '@merchant_account_no',
          value: merchantAccountNo,
        },
        {
          name: '@latest_statuses',
          value: latestTrackingStatuses,
        },
        {
          name: '@start_date',
          value: last60Days,
        },
        {
          name: '@today',
          value: today,
        },
      ],
    };

    return this.find(querySpec);
  }
}
