import { StatusMappingService } from './../../shared/StatusMappingService';
import { ManifestItemDaoService } from './manifest-dao.service';
jest.mock('./../../utils', () => {
  return {
    CrtCounterEncrypt: jest.fn().mockImplementation((value) => value),
  };
});
jest.mock('../base.service');

const service = new ManifestItemDaoService({} as any);
describe('Test manifest dao service', () => {
  describe('Test getParcelByTrackingNoOrId function', () => {
    test('When ther is no parcel found, return null', async () => {
      //Assign
      service.find = jest.fn().mockResolvedValue([]);

      //Act
      const result = await service.getParcelByTrackingNoOrId('123');

      //Assert
      expect(result).toBeNull();
    });

    test('When ther is parcel found, return value as expected', async () => {
      //Assign
      const parcel = { id: 'PLX123' };
      service.find = jest.fn().mockResolvedValue([parcel]);

      //Act
      const result = await service.getParcelByTrackingNoOrId('PLX123');

      //Assert
      expect(result).toEqual(parcel);
    });
  });

  describe('Test getParcelsOfMerchant function', () => {
    test('When calling function, return value as expected', async () => {
      //Assign
      StatusMappingService.getManifestStatus = jest.fn().mockImplementation((value) => value);
      service.find = jest.fn();

      //Act
      await service.getParcelsOfMerchant('iherb');

      //Assert
      expect(service.find).toHaveBeenCalled();
    });
  });
});
