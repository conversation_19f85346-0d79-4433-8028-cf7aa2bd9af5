import { OperationHubDAOService } from './operation-hub-dao.service';
import * as helper from './operation-hub.helper';
jest.mock('./../../utils', () => {
  return {
    CrtCounterEncrypt: jest.fn().mockImplementation((value) => value),
  };
});
jest.mock('../base.service');
const service = new OperationHubDAOService();

describe('Test operation hub dao service', () => {
  describe('Test getByCountry function', () => {
    test('When having exception, return an empty result', async () => {
      //Assign
      jest.spyOn(helper, 'decryptOpsHub').mockImplementation((value) => value);
      service.find = jest.fn().mockRejectedValue('error');

      //Act
      const result = await service.getByCountry('Singapore');

      //Assert
      expect(result.length).toEqual(0);
    });
  });

  describe('Test getByCountry function', () => {
    test('When having no exception, return value as expected', async () => {
      //Assign
      const ophubs = [{ name: 'SIN01' }];
      jest.spyOn(helper, 'decryptOpsHub').mockImplementation((value) => value);
      service.find = jest.fn().mockResolvedValue(ophubs);

      //Act
      const result = await service.getByCountry('Singapore');

      //Assert
      expect(result).toEqual(ophubs);
    });
  });
});
