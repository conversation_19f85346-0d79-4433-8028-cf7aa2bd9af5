import OperationHubModel from './operation-hub.model';
import { CrtCounterDecrypt } from '../../utils';

export function decryptOpsHub(operationHub: OperationHubModel): OperationHubModel {
  return {
    ...operationHub,
    airport_code: operationHub.airport_code ? CrtCounterDecrypt(operationHub.airport_code) : '',
    address_line_1: CrtCounterDecrypt(operationHub.address_line_1),
    address_line_2: operationHub.address_line_2 ? CrtCounterDecrypt(operationHub.address_line_2) : '',
    city_suburb: operationHub.city_suburb ? CrtCounterDecrypt(operationHub.city_suburb) : '',
    state: operationHub.state ? CrtCounterDecrypt(operationHub.state) : '',
    post_code: operationHub.post_code ? CrtCounterDecrypt(operationHub.post_code) : '',
    country: CrtCounterDecrypt(operationHub.country),
    phone_country_code: operationHub.phone_country_code ? CrtCounterDecrypt(operationHub.phone_country_code) : '',
    phone: operationHub.phone ? CrtCounterDecrypt(operationHub.phone) : '',
    email: Array.isArray(operationHub.email) ? operationHub.email.map((item) => CrtCounterDecrypt(item)) : [],
  };
}
