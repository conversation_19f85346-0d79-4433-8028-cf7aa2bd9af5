export default interface OperationHubModel {
  operation_hub: string;
  operation_hub_name: string;
  airport_code: string;
  address_line_1: string;
  address_line_2: string;
  city_suburb: string;
  state: string;
  post_code: string;
  country: string;
  phone_country_code: string;
  phone: string;
  email: string[];
  id: string;
  cutoff_time: string;
  time_zone: string;
  fmd_provider_name: string[];
  opening_hours: IOpenHours[];
  show_contact_on_PLS_parcel_label: boolean;
}

interface IOpenHours {
  day_of_week: number;
  start_time: string;
  end_time: string;
}
