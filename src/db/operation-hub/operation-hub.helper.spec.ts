import * as helper from './operation-hub.helper';
import OperationHubModel from './operation-hub.model';
jest.mock('../../utils', () => {
  return {
    CrtCounterDecrypt: jest.fn().mockImplementation((value) => value),
  };
});
describe('test operation-hub.helper', () => {
  describe('test decryptOpsHub', () => {
    test('it should return correct result', () => {
      // Arrange
      const operationHub: OperationHubModel = {
        airport_code: 'airport',
        address_line_1: 'address1',
        address_line_2: 'address2',
        city_suburb: 'suburb',
        state: 'state',
        post_code: 'post_code',
        country: 'country',
        phone_country_code: 'phone_code',
        phone: 'phone',
        email: ['email'],
        operation_hub_name: 'operation_hub_name',
        operation_hub: 'operation_hub',
        id: 'id',
        cutoff_time: 'cutoff',
        time_zone: 'time_zone',
        fmd_provider_name: ['fmd'],
        opening_hours: [{ day_of_week: 7, start_time: 'start', end_time: 'end' }],
        show_contact_on_PLS_parcel_label: true,
      };

      // Act
      const result = helper.decryptOpsHub(operationHub);

      // Assert
      expect(result).toEqual(operationHub);
    });
  });
});
