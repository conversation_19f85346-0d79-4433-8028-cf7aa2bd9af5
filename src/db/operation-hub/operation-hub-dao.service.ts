import { SqlQuerySpec } from '@azure/cosmos';
import { Injectable } from '@nestjs/common';
import { AppConfig } from '../../resources';
import { BaseService } from '../base.service';
import OperationHubModel from './operation-hub.model';
import { CrtCounterEncrypt } from '../../utils';
import { decryptOpsHub } from './operation-hub.helper';

@Injectable()
export class OperationHubDAOService extends BaseService<OperationHubModel> {
  constructor() {
    super(AppConfig.db.operationHub);
  }

  async getByCountry(country: string) {
    try {
      const querySpec: SqlQuerySpec = {
        query: `SELECT * FROM c WHERE c.country = @country`,
        parameters: [
          {
            name: '@country',
            value: CrtCounterEncrypt(country),
          },
        ],
      };
      const res = await this.find(querySpec);
      return res.map((opsHub) => decryptOpsHub(opsHub));
    } catch (e) {
      console.log('operation-hub-dao.getByCountry', 'error', e);
      return [];
    }
  }
}
