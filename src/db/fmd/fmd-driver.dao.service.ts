import { SqlQuerySpec } from '@azure/cosmos';

import { AppConfig } from '../../resources';
import { BaseService } from '../base.service';
import { FmdDriver } from './fmd-dao.model';

export class FmdDriverDaoService extends BaseService<FmdDriver> {
  constructor() {
    super(AppConfig.db.fmd);
  }

  async getDriver(account: string) {
    const querySpec: SqlQuerySpec = {
      query: `SELECT c.id, c.name, c.email, c.companyId
              FROM c
              Where c.type = @partitionKey
                AND (c.email = @account OR c.name = @account)`,
      parameters: [
        {
          name: '@partitionKey',
          value: 'driver',
        },
        {
          name: '@account',
          value: account,
        },
      ],
    };
    const res = await this.find(querySpec);
    return res[0];
  }
}
