import { SqlQuerySpec } from '@azure/cosmos';
import { Injectable } from '@nestjs/common';
import { AppConfig } from '../../resources';
import { BaseService } from '../base.service';
import { JobTransactionType, JobType } from './../../utils';
import { FmdJob } from './fmd-dao.model';

@Injectable()
export class FmdJobDaoService extends BaseService<FmdJob> {
  constructor() {
    super(AppConfig.db.fmd);
  }

  createPickedUpJob(driverEmail: string, merchantNo: string) {
    return this.create({
      type: JobType.JOB,
      transaction_type: JobTransactionType.PICKED_UP,
      created_at: new Date().toISOString(),
      driver_email: driverEmail,
      parcel_list: [],
      merchant_no: merchantNo,
    });
  }

  getPickedUpJobByUser(encryptedUserEmail: string) {
    const querySpec: SqlQuerySpec = {
      query: `SELECT TOP 10 c.created_at, c.parcel_list, c.merchant_no, c.id, c.driver_email
              FROM c
              WHERE c.transaction_type = @transaction_type AND c.driver_email = @driver_email
              ORDER BY c._ts DESC`,
      parameters: [
        {
          name: '@transaction_type',
          value: JobTransactionType.PICKED_UP,
        },
        {
          name: '@driver_email',
          value: encryptedUserEmail,
        },
      ],
    };

    return this.find(querySpec);
  }
}
