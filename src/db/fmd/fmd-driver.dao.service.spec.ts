import { SqlQuerySpec } from '@azure/cosmos';
import { FmdDriverDaoService } from './fmd-driver.dao.service';

describe('FmdDriverDaoService', () => {
  let fmdDriverDaoService: FmdDriverDaoService;

  beforeEach(() => {
    fmdDriverDaoService = new FmdDriverDaoService();
    fmdDriverDaoService.find = jest.fn(); // Mock 'find' method from BaseService
  });

  it('should call find with correct query and parameters when driver is found', async () => {
    // Arrange
    const account = '<EMAIL>';
    const mockDriver = { id: '1', name: '<PERSON>', email: account, companyId: '123' };
    (fmdDriverDaoService.find as jest.Mock).mockResolvedValue([mockDriver]);

    // Act
    const result = await fmdDriverDaoService.getDriver(account);

    // Assert
    const expectedQuerySpec: SqlQuerySpec = {
      query: `SELECT c.id, c.name, c.email, c.companyId
              FROM c
              Where c.type = @partitionKey
                AND (c.email = @account OR c.name = @account)`,
      parameters: [
        { name: '@partitionKey', value: 'driver' },
        { name: '@account', value: account },
      ],
    };

    expect(fmdDriverDaoService.find).toHaveBeenCalledWith(expectedQuerySpec); // Check correct query
    expect(result).toEqual(mockDriver); // Check returned result matches
  });

  it('should return undefined if no driver is found', async () => {
    // Arrange
    (fmdDriverDaoService.find as jest.Mock).mockResolvedValue([]); // No results

    // Act
    const result = await fmdDriverDaoService.getDriver('<EMAIL>');

    // Assert
    expect(result).toBeUndefined(); // Should return undefined when no driver is found
  });
});
