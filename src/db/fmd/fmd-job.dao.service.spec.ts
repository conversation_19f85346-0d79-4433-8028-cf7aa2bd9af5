import { FmdJobDaoService } from './fmd-job.dao.service';
jest.mock('../base.service');

const service = new FmdJobDaoService();
let findMock: jest.Mock;

beforeEach(() => {
  findMock = jest.fn();
  // Mock the 'find' method to avoid making actual database calls
  jest.spyOn(service, 'find').mockImplementation(findMock);
});

describe('Test fmd drive dao service', () => {
  describe('Test createPickedUpJob function', () => {
    test('When call function, should return value as expected', async () => {
      //Arrange
      const date = new Date('2023-07-26T08:49:29.382Z');
      global.Date = jest.fn(() => date) as any;
      const expResult = {
        created_at: date.toISOString(),
        driver_email: '<EMAIL>',
        transaction_type: 'picked_up',
        merchant_no: 'merchantNo',
        parcel_list: [],
        type: 'job',
      };
      service.create = jest.fn();

      //Act
      service.createPickedUpJob('<EMAIL>', 'merchantNo');

      //Assert
      expect(service.create).toHaveBeenCalledWith(expResult);
    });
  });

  describe('Test getPickedUpJobByUser function', () => {
    test('When call function, should return value as expected', async () => {
      //Arrange
      //Act
      service.getPickedUpJobByUser('<EMAIL>');

      //Assert
      expect(findMock).toHaveBeenCalled();
    });
  });
});
