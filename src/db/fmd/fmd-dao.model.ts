import { ItemDefinition, Resource } from '@azure/cosmos';
import { JobTransactionType } from '../../utils';

type FmdBase = Partial<Resource> &
  ItemDefinition & {
    type: 'company' | 'driver' | 'job';
  };

export interface FmdCompany extends FmdBase {
  country: string;
  company: string;
}

export interface FmdDriver extends FmdBase {
  name: string;
  email: string;
  companyId: string;
  last_activity?: string | Date;
}

export interface Fmd extends FmdCompany, FmdDriver, FmdJob {}

export interface FmdJob extends FmdBase {
  transaction_type: JobTransactionType.PICKED_UP;

  driver_email: string;

  // for pickup tranx
  parcel_id?: string;
  tracking_id?: string;
}

export type FmdScanParcelPayload = {
  jobId: string;
  inMerchantPickupList: boolean;
  parcelBarcode: string;
  merchantAccountNo: string;
};
