import { FmdCompanyDaoService } from './fmd-company.dao.service';

describe('FmdCompanyDaoService', () => {
  let fmdCompanyDaoService: FmdCompanyDaoService;

  beforeEach(() => {
    fmdCompanyDaoService = new FmdCompanyDaoService();
    // Mock 'getItem' method from BaseService
    fmdCompanyDaoService.getItem = jest.fn();
  });

  it('should call getItem with correct id and partitionKey', async () => {
    // Arrange
    const companyId = 'company123';
    const mockCompany = { id: companyId, name: 'Test Company', country: 'US' };
    (fmdCompanyDaoService.getItem as jest.Mock).mockResolvedValue(mockCompany);

    // Act
    const result = await fmdCompanyDaoService.getItem(companyId);

    // Assert
    expect(result).toEqual(mockCompany); // Ensure returned company data is correct
  });

  it('should return undefined if no company is found', async () => {
    // Arrange
    (fmdCompanyDaoService.getItem as jest.Mock).mockResolvedValue(undefined); // No company found

    // Act
    const result = await fmdCompanyDaoService.getItem('nonexistent123');

    // Assert
    expect(result).toBeUndefined(); // Should return undefined if no company is found
  });
});
