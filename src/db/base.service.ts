import {
  BulkOperationType,
  Container,
  CosmosClient,
  Database,
  RequestOptions,
  Resource,
  SqlQuerySpec,
} from '@azure/cosmos';
import chunk from 'lodash/chunk';
import { AppConfig } from '../resources';
import { batchPartialOperationsGenerator, generatePartialOperationsFromObj } from '../utils/BaseDaoUtils';
import { chunkArrayGenerator, sleep } from '../utils/CommonUtils';
import { logDebug, logError } from '../utils/LogUtils';

export type BulkUpdateSuccessItem = {
  originalIndex: number;
  item: any;
};
export type BulkUpdateErrorItem = {
  originalIndex: number;
  error: {
    statusCode?: number;
    code?: string | number;
    error?: string;
  };
  item: any;
};

export type BulkUpdateResult = {
  successItems: BulkUpdateSuccessItem[];
  errorItems: BulkUpdateErrorItem[];
};

export abstract class BaseService<T> {
  public cosmosClient: CosmosClient;
  private collectionName: string;
  public database: Database;
  private container: Container;
  bulkThreshold: number;
  sleepTimeMs: number;
  archiveDao: any;

  constructor(collectionName: string, archiveDao?, bulkThreshold = 30, sleepTimeMs = 1000) {
    this.collectionName = collectionName;
    this.cosmosClient = new CosmosClient({
      endpoint: AppConfig.db.host,
      key: AppConfig.db.authKey,
    });
    this.archiveDao = archiveDao;
    this.database = this.cosmosClient.database(AppConfig.db.databaseId);
    this.container = this.database.container(this.collectionName);
    this.bulkThreshold = Number(bulkThreshold);
    if (Number.isNaN(this.bulkThreshold)) {
      throw new Error(`baseDao.bulkThreshold must be a number!`);
    }
    this.sleepTimeMs = sleepTimeMs;
  }

  public async getItem(id: string, _partitionKey?: string): Promise<(T & Resource) | undefined> {
    try {
      const item = this.container.item(id, _partitionKey);
      const { resource } = await item.read<T>();
      return resource;
    } catch (err) {
      console.log(this.collectionName, 'getItem', err);
      return undefined;
    }
  }

  public async find(query: string | SqlQuerySpec): Promise<(T & Resource)[]> {
    try {
      const items = this.container.items.query<T & Resource>(query);
      const result = await items.fetchAll();
      return result.resources;
    } catch (err) {
      console.log(this.collectionName, 'find', err);
      return [];
    }
  }

  public async create(item: T): Promise<(T & Resource) | undefined> {
    try {
      const { resource } = await this.container.items.create<T>(item);
      return resource;
    } catch (err) {
      console.log(this.collectionName, 'create', err);
      return undefined;
    }
  }

  public async update(updateItem: any, partitionKey?: string) {
    const id = updateItem.id;
    const dbItem = await this.getItem(id, partitionKey);
    const mergedItem = { ...dbItem, ...updateItem };

    let options: RequestOptions;
    if (updateItem._etag) {
      options = {
        accessCondition: {
          type: 'IfMatch',
          condition: updateItem._etag,
        },
      };
    }
    const { resource } = await this.container.item(id, partitionKey).replace(mergedItem, options);
    return resource;
  }

  //This function temporarily not working without partitionKey, will use this function after manifest_item has partitionKey.
  async DRAFFbulkUpdate(itemList, limit = this.bulkThreshold, sleepMs = this.sleepTimeMs) {
    const successItems = new Map();
    const errorItems = new Map();

    const itemOperationList = batchPartialOperationsGenerator(itemList, limit, BulkOperationType.Patch);
    for (const itemOperations of itemOperationList) {
      const operations = itemOperations.map((item) => item.operation);
      logDebug('bulkUpdate', `CosmosDB, container=${this.container.id}`, operations);
      const results = await this.container.items.bulk(operations, {
        continueOnError: true,
      });
      for (let i = 0; i < results.length; i++) {
        const updateResult = results[i];
        const indexInItemList = itemOperations[i].originalIndex;
        if (updateResult.statusCode === 200) {
          successItems.set(indexInItemList, {
            originalIndex: indexInItemList,
            item: updateResult.resourceBody,
          });
        } else {
          errorItems.set(indexInItemList, {
            originalIndex: indexInItemList,
            error: updateResult,
            item: itemList[indexInItemList],
          });
        }
      }
      await sleep(sleepMs);
    }
    if (this.archiveDao) {
      const succeededItems = [];
      for (const successItem of successItems.values()) {
        succeededItems.push(itemList[successItem.originalIndex]);
      }
      this.archiveDao
        .bulkUpdate(succeededItems, 10, 1000)
        .then((result) => {
          for (const item of result.errorItems) {
            const payload = succeededItems[item.originalIndex];
            logError(
              `bulkUpdate update manifestItemArchive failed for parcelId: ${payload.id}`,
              `CosmosDB, container=${this.archiveDao.container.id}`,
              '\nerror: ',
              item.error,
              '\npayload: ',
              payload,
            );
          }
        })
        .catch((archiveErr) => {
          logError('bulkUpdate', `CosmosDB, container=${this.archiveDao.container.id}`, itemList, archiveErr);
        });
    }

    return {
      successItems: [...successItems.values()],
      errorItems: [...errorItems.values()],
    };
  }

  async patch(item) {
    const { id, partitionKey } = item;
    const operations = generatePartialOperationsFromObj(item);
    if (!operations.length) {
      throw new Error('Patch update need at least 1 operation!');
    }
    const instance = this.container.item(id, partitionKey);
    let doc;
    for (const chunk10Operation of chunk(operations, 10)) {
      logDebug('patch', `CosmosDB, container=${this.container.id}`, chunk10Operation);
      doc = await instance.patch(chunk10Operation);
    }
    if (this.archiveDao) {
      try {
        await this.archiveDao.patch(item, partitionKey);
      } catch (archiveErr) {
        logError('patch', `CosmosDB, container=${this.archiveDao.container.id}`, item, archiveErr);
      }
    }
    return doc.resource;
  }

  async bulkUpdate(itemList, limit = 20, sleepMs = 1000): Promise<BulkUpdateResult> {
    const successItems = [];
    const errorItems = [];
    let batchNo = 0;
    for (const items of chunkArrayGenerator(itemList, limit)) {
      const results = await Promise.allSettled(items.map((item) => this.patch(item)));
      for (const [index, result] of results.entries()) {
        const originalIndex = batchNo * limit + index;
        if (result.status === 'fulfilled') {
          const item = result.value;
          successItems.push({
            originalIndex,
            item,
          });
        } else {
          const item = items[originalIndex];
          const errorMessage = result.reason?.body?.message || result.reason;
          errorItems.push({
            originalIndex,
            error: {
              statusCode: result.reason?.code,
              code: result.reason?.body?.code,
              error: errorMessage,
            },
            item,
          });
        }
      }
      batchNo += 1;
      await sleep(sleepMs);
    }

    return {
      successItems,
      errorItems,
    };
  }
}
