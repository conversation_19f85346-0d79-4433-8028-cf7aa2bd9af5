import { Controller, Get, Post, Request, Response, UseGuards } from '@nestjs/common';
import { AppService } from './app.service';
import { AuthGuard } from './auth/auth.guard';
import { AuthService } from './auth/auth.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService, private readonly authService: AuthService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('/csrf-token')
  async name(@Request() req, @Response() res) {
    return res.status(200).json({ 'XSRF-TOKEN': 'token' });
  }

  @UseGuards(AuthGuard)
  @Post('auth/login')
  async login(@Request() req, @Response() res) {
    const loginDetail = await this.authService.login(req.decoded.emails[0]);
    if (!loginDetail) {
      return res.status(404).json({
        message: 'Account not found',
        description: 'The account is not in the system',
      });
    }
    return res.status(200).json(loginDetail);
  }

  @UseGuards(AuthGuard)
  @Post('auth/logout')
  async logout(@Request() req, @Response() res) {
    req.cookies = null;
    await this.authService.clearCache(req.token);
    res.clearCookie('pls_token');
    res.clearCookie('XSRF-TOKEN');
    res.clearCookie('_csrf');
    res.sendStatus(200);
  }
}
