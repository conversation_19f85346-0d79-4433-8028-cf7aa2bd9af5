import axios from 'axios';
import { fetchKeysB2C } from './authUtils';

afterAll(() => {
  jest.clearAllMocks();
});

describe('test fetchKeysB2C function', () => {
  test('When call function, it runs as expectation', async () => {
    //Assign
    axios.get = jest.fn().mockResolvedValue({ data: 'response' });

    //Act
    const result = await fetchKeysB2C('uri');

    //Assert
    expect(result).toEqual('response');
  });
});
