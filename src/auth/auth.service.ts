import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import atob from 'atob';
import { Cache } from 'cache-manager';
import jwt from 'jsonwebtoken';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import getPem from 'rsa-pem-from-mod-exp';
import { FmdCompanyDaoService } from '../db/fmd/fmd-company.dao.service';
import { FmdDriverDaoService } from '../db/fmd/fmd-driver.dao.service';
import { AppConfig } from '../resources';
import { CacheName } from '../utils/Enum';
import { fetchKeysB2C } from './authUtils';

@Injectable()
export class AuthService {
  constructor(
    public fmdDriverDaoClient: FmdDriverDaoService,
    public fmdCompanyDaoClient: FmdCompanyDaoService,
    @Inject(CACHE_MANAGER) public cacheManager: Cache,
  ) {}

  async validateToken(req: any, res: any) {
    const token = isEmpty(req.headers.authorization) ? '' : req.headers.authorization;
    req.token = token;

    const keysClaim = await this.verifyKey();
    let result;
    if (token) {
      const base64Url = token.split('.')[0];
      const { kid, alg } = JSON.parse(atob(base64Url));
      const { n, e } = Array.isArray(keysClaim) && keysClaim.filter((k) => k.kid === kid)[0];
      const publicKey = getPem(n, e);

      try {
        // verify makes sure that the token hasn't expired
        result = jwt.verify(token, publicKey, { algorithms: [alg] });
        // Let's pass back the decoded token to the request object
        const expiredTime = get(result, 'exp', 0);
        const isExpired = expiredTime - Date.now() / 1000 <= 0;
        if (isExpired) {
          await this.cacheManager.del(token); //delete token's status from cache
          throw new UnauthorizedException('Token is expired');
        }
        req.decoded = result;

        // We call next to pass execution to the subsequent middleware
        this.logActivityTime(result.emails[0]);
        const cacheToken = await this.cacheManager.get(token);
        if (cacheToken != null && !cacheToken) {
          throw new UnauthorizedException('Token is expired');
        } else if (cacheToken == null) {
          await this.cacheManager.set(token, true, 1000 * 3600 * 24); //save token's status to valid in 1 day
        }
        return true;
      } catch (err) {
        // Throw an error just in case anything goes wrong with verification
        await this.cacheManager.del(token); //delete token's status from cache
        throw new UnauthorizedException(err.message);
      }
    } else {
      throw new UnauthorizedException('Token is expired');
    }
  }

  /**
   * Verify if keys from client is match with keys from current B2C policy
   * @returns key claimed from b2c
   */
  async verifyKey() {
    const jwks_uri = `https://${AppConfig.azureB2C.tenantName}.b2clogin.com/${AppConfig.azureB2C.tenantName}.onmicrosoft.com/discovery/v2.0/keys?p=${AppConfig.azureB2C.policy}`;
    let keysClaim;
    const cachePublicKeys = await this.cacheManager.get(CacheName.PUBLIC_KEYS);
    if (cachePublicKeys) {
      keysClaim = cachePublicKeys;
    } else {
      try {
        const { keys } = await fetchKeysB2C(jwks_uri);
        await this.cacheManager.set(CacheName.PUBLIC_KEYS, keys);
        keysClaim = keys;
      } catch (err) {
        console.log('auth.service validateToken get key from b2c', err);
        throw new UnauthorizedException('Token is expired');
      }
    }
    return keysClaim;
  }

  async login(userName: string) {
    try {
      const driverInfo = await this.fmdDriverDaoClient.getDriver(userName);
      if (!driverInfo) {
        console.log('auth.service login cannot find driver account info in our database:', userName);
      }
      const fmdInfo = await this.fmdCompanyDaoClient.getItem(driverInfo.companyId);
      return {
        accountName: driverInfo.name,
        driverId: driverInfo.id,
        companyId: driverInfo.companyId,
        email: driverInfo.email,
        fmdName: fmdInfo.company,
      };
    } catch (error) {
      console.log('auth.service', error);
    }
    return null;
  }

  async logActivityTime(userName: string) {
    try {
      const driverInfo = await this.fmdDriverDaoClient.getDriver(userName);
      if (!driverInfo) {
        console.log('auth.service logActivityTime account does not exist in database:', userName);
      } else {
        driverInfo.last_activity = new Date();
        await this.fmdDriverDaoClient.update(driverInfo, 'driver');
      }
    } catch (error) {
      console.log('auth.service logActivityTime can not update activity with', userName);
    }
  }

  async clearCache(token: string) {
    await this.cacheManager.del(CacheName.PUBLIC_KEYS);
    await this.cacheManager.set(token, false);
  }
}
