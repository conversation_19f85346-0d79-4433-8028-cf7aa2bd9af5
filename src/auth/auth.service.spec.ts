import { CacheModule } from '@nestjs/cache-manager';
import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import atob from 'atob';

jest.mock('atob');
jest.mock('./authUtils');
console.log = jest.fn();
import jwt from 'jsonwebtoken';
import { fetchKeysB2C } from './authUtils';
import { FmdCompanyDaoService } from '../db/fmd/fmd-company.dao.service';
import { FmdDriverDaoService } from '../db/fmd/fmd-driver.dao.service';

describe('AuthService', () => {
  let service: AuthService;

  beforeEach(async () => {
    jest.clearAllMocks();
    const module: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register()],
      providers: [AuthService, FmdCompanyDaoService, FmdDriverDaoService],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('test verifyKey function', () => {
    test('When fetch keys B2C failed, throw token expired exception', async () => {
      //Assign
      service.cacheManager.get = jest.fn().mockResolvedValue('');
      const fetchKeysB2CMock = fetchKeysB2C as jest.MockedFunction<typeof fetchKeysB2C>;
      fetchKeysB2CMock.mockImplementation((uri) => {
        throw new Error('Fetch keys failed');
      });

      try {
        //Act
        await service.verifyKey();
      } catch (e) {
        //Assert
        expect(e.message).toEqual('Token is expired');
      }
    });

    test('When fetch keys B2C success, return keys', async () => {
      //Assign
      service.cacheManager.get = jest.fn().mockResolvedValue('');
      service.cacheManager.set = jest.fn();
      const fetchKeysB2CMock = fetchKeysB2C as jest.MockedFunction<typeof fetchKeysB2C>;
      fetchKeysB2CMock.mockResolvedValue({ keys: 'keys' });

      //Act
      const keys = await service.verifyKey();

      //Assert
      expect(keys).toEqual('keys');
      expect(service.cacheManager.set).toHaveBeenCalled();
    });

    test('When have keys in cache, return keys', async () => {
      //Assign
      service.cacheManager.get = jest.fn().mockResolvedValue('keys');
      service.cacheManager.set = jest.fn();

      //Act
      const keys = await service.verifyKey();

      //Assert
      expect(keys).toEqual('keys');
      expect(service.cacheManager.set).not.toHaveBeenCalled();
    });
  });

  describe('test validateToken function', () => {
    test('When have no token, throw unauthorized exception', async () => {
      //Assign
      const req = {
        headers: {
          authorization: '',
        },
        cookies: {
          pls_token: '',
        },
      };
      service.verifyKey = jest.fn();

      try {
        //Act
        await service.validateToken(req, null);
      } catch (e) {
        //Assert
        expect(e.message).toEqual('Token is expired');
      }
    });

    test('When have token but verify failed, throw unauthorized exception', async () => {
      //Assign
      const req = {
        headers: {
          authorization: 'token.',
        },
      };
      const res = {
        cookie: jest.fn(),
      };
      service.verifyKey = jest.fn().mockResolvedValue([{ kid: 'kid', n: 'n', e: 'e' }]);
      const atobMock = atob as jest.MockedFunction<typeof atob>;
      atobMock.mockReturnValue(JSON.stringify({ kid: 'kid', alg: 'alg' }));
      jwt.verify = jest.fn().mockImplementation((token, key, options) => {
        throw new Error('Verify token failed');
      });

      try {
        //Act
        await service.validateToken(req, res);
      } catch (e) {
        //Assert
        expect(e.message).toEqual('Verify token failed');
      }
    });

    test('When token expired, throw unauthorized exception', async () => {
      //Assign
      const req = {
        headers: {
          authorization: 'token.',
        },
      };
      const res = {
        cookie: jest.fn(),
      };
      service.verifyKey = jest.fn().mockResolvedValue([{ kid: 'kid', n: 'n', e: 'e' }]);
      const atobMock = atob as jest.MockedFunction<typeof atob>;
      atobMock.mockReturnValue(JSON.stringify({ kid: 'kid', alg: 'alg' }));
      jwt.verify = jest.fn().mockReturnValue({ exp: 1 });
      service.cacheManager.del = jest.fn();

      try {
        //Act
        await service.validateToken(req, res);
      } catch (e) {
        //Assert
        expect(e.message).toEqual('Token is expired');
        expect(service.cacheManager.del).toHaveBeenCalled();
      }
    });

    test('When token in cache is undefined, throw unauthorized exception', async () => {
      //Assign
      const req = {
        headers: {
          authorization: 'token.',
        },
      };
      const res = {
        cookie: jest.fn(),
      };
      service.verifyKey = jest.fn().mockResolvedValue([{ kid: 'kid', n: 'n', e: 'e' }]);
      const atobMock = atob as jest.MockedFunction<typeof atob>;
      atobMock.mockReturnValue(JSON.stringify({ kid: 'kid', alg: 'alg' }));
      jwt.verify = jest.fn().mockReturnValue({ exp: Date.now() + 1, emails: ['<EMAIL>'] });
      service.cacheManager.get = jest.fn().mockResolvedValue(undefined);

      try {
        //Act
        await service.validateToken(req, res);
      } catch (e) {
        //Assert
        expect(e.message).toEqual('Token is expired');
      }
    });

    test('When token in cache is null, return true', async () => {
      //Assign
      const req = {
        headers: {
          authorization: 'token.',
        },
      };
      const res = {
        cookie: jest.fn(),
      };
      service.verifyKey = jest.fn().mockResolvedValue([{ kid: 'kid', n: 'n', e: 'e' }]);
      const atobMock = atob as jest.MockedFunction<typeof atob>;
      atobMock.mockReturnValue(JSON.stringify({ kid: 'kid', alg: 'alg' }));
      jwt.verify = jest.fn().mockReturnValue({ exp: Date.now() + 1, emails: ['<EMAIL>'] });
      service.cacheManager.get = jest.fn().mockResolvedValue(null);
      service.cacheManager.set = jest.fn();

      //Act
      const result = await service.validateToken(req, res);
      //Assert
      expect(result).toEqual(true);
      expect(service.cacheManager.set).toHaveBeenCalledWith('token.', true, 86400000);
    });
  });

  describe('test login function', () => {
    test('When have error, log the error then return null', async () => {
      //Assign
      service.fmdDriverDaoClient.getDriver = jest.fn().mockResolvedValueOnce(undefined);
      service.fmdCompanyDaoClient.getItem = jest.fn().mockResolvedValueOnce({
        company: 'Apple',
      });

      //Act
      const result = await service.login('');

      //Assert
      expect(result).toEqual(null);
    });

    test('When have error, return value as expectation', async () => {
      //Assign
      service.fmdCompanyDaoClient.getItem = jest.fn().mockResolvedValue({
        company: 'Apple',
      });
      service.fmdDriverDaoClient.getDriver = jest.fn().mockResolvedValue({
        name: 'Foxconn',
        id: 'FXN',
        companyId: 'FXN01',
        email: '<EMAIL>',
      });

      //Act
      const result = await service.login('');
      //Assert
      expect(result).toEqual({
        accountName: 'Foxconn',
        driverId: 'FXN',
        companyId: 'FXN01',
        email: '<EMAIL>',
        fmdName: 'Apple',
      });
    });
  });

  describe('test logActivityTime function', () => {
    test('When have error, log the error', async () => {
      //Assign
      service.fmdDriverDaoClient.getDriver = jest.fn().mockImplementation((userName) => {
        throw new Error('Error when get fmd driver');
      });
      try {
        //Act
        await service.logActivityTime('');
      } catch (error) {
        //Assert
        expect(error.message).toEqual('Error when get fmd driver');
      }
    });

    test('When have no driver info, log the message', async () => {
      //Assign
      service.fmdDriverDaoClient.getDriver = jest.fn().mockResolvedValue(null);
      //Act
      await service.logActivityTime('David');
      //Assert
      expect(console.log).toHaveBeenCalledWith(
        'auth.service logActivityTime account does not exist in database:',
        'David',
      );
    });

    test('When have driver info, update it to db', async () => {
      //Assign
      service.fmdDriverDaoClient.update = jest.fn();
      service.fmdDriverDaoClient.getDriver = jest.fn().mockResolvedValue({ driverId: '123' });
      //Act
      await service.logActivityTime('David');
      //Assert
      expect(console.log).not.toHaveBeenCalled();
      expect(service.fmdDriverDaoClient.update).toHaveBeenCalled();
    });
  });

  describe('test clearCache function', () => {
    test('When call this function, ensure that it runs as expectation', async () => {
      //Assign
      service.cacheManager.del = jest.fn();
      service.cacheManager.set = jest.fn();

      //Act
      await service.clearCache('mytoken');

      //Assert
      expect(service.cacheManager.del).toHaveBeenCalledWith('public-keys');
      expect(service.cacheManager.set).toHaveBeenCalledWith('mytoken', false);
    });
  });
});
