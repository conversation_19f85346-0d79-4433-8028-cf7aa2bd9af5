import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';

import { FmdCompanyDaoService } from '../db/fmd/fmd-company.dao.service';
import { FmdDriverDaoService } from '../db/fmd/fmd-driver.dao.service';

import { AuthService } from './auth.service';

@Module({
  imports: [CacheModule.register()],
  exports: [AuthService],
  providers: [FmdDriverDaoService, FmdCompanyDaoService, AuthService],
})
export class AuthModule {}
