import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { DestinationModule } from './modules/destination/destination.module';
import { FmdModule } from './modules/fmd/fmd.module';
import { OperationHubsModule } from './modules/operation-hubs/operation-hubs.module';
import { ParcelModule } from './modules/parcel/parcel.module';
import { MerchantModule } from './modules/merchant/merchant.module';

@Module({
  imports: [AuthModule, DestinationModule, OperationHubsModule, FmdModule, ParcelModule, MerchantModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
