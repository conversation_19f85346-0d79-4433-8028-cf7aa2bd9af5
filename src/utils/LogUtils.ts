export function logError(message, funcName, ...others) {
  console.error(`type=ERROR, function=${funcName}, message=${message}, ${formatMessages(...others)}`);
}

export function logInfo(message, funcName, ...others) {
  console.info(`type=INFO, function=${funcName}, message=${message}, ${formatMessages(...others)}`);
}

export function logDebug(message, funcName, ...others) {
  console.log(`type=DEBUG, function=${funcName}, message=${message}, ${formatMessages(...others)}`);
}

export function logWarn(message, funcName, ...others) {
  console.warn(`type=WARN, function=${funcName}, message=${message}, ${formatMessages(...others)}`);
}

export function logMessageFromClient(...messages) {
  console.log(`type=CLIENT, message=${formatMessages(...messages)}`);
}

/**
 * Formatting a list of messages from any type to a string.
 * @param {Array<unknown>} messages
 * @returns a printable string.
 */
export function formatMessages(...messages) {
  const formatted = messages.map((msg) => {
    if (msg instanceof Error) {
      return msg.stack;
    }
    if (typeof msg === 'object') {
      return JSON.stringify(msg, getCircularReplacer());
    }
    return msg;
  });

  return formatted.join(', ');
}

export function getCircularReplacer() {
  const seen = new WeakSet();
  return (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return;
      }
      seen.add(value);
    }
    return value;
  };
}
