import { PatchOperationType } from '@azure/cosmos';
import { chunkArrayGenerator } from './CommonUtils';

export function generatePartialOperationsFromObj(obj) {
  const operations = [];
  for (const [key, value] of Object.entries(obj)) {
    if (key === 'id' || key === 'partitionKey') continue;
    if (value === undefined) continue;
    if (key.includes('___remove')) {
      operations.push({
        op: PatchOperationType.remove,
        path: `/${key.split('___')[0]}`,
      });
    } else if (Array.isArray(value)) {
      operations.push(..._generatePartialOperationFromArray(key, value));
    } else {
      operations.push({
        op: PatchOperationType.set,
        path: `/${key}`,
        value: value,
      });
    }
  }
  return operations;
}

export function _generatePartialOperationFromArray(key, value) {
  const operations = [];
  if (!key.includes('___')) {
    for (const item of value) {
      operations.push(
        ...[
          {
            op: PatchOperationType.set,
            path: `/${key}/-`,
            value: item,
          },
        ],
      );
    }
  } else if (key.includes('___overwrite')) {
    operations.push({
      op: PatchOperationType.set,
      path: `/${key.split('___')[0]}`,
      value: value,
    });
  }
  return operations;
}

export function* batchPartialOperationsGenerator(itemArr, batchSize, operationType) {
  let i = 0;
  const operations = [];
  for (const [index, item] of itemArr.entries()) {
    const partialOperations = generatePartialOperationsFromObj(item);
    if (partialOperations.length > 10) {
      for (const batch10PartialOperation of chunkArrayGenerator(partialOperations, 10)) {
        operations.push({
          originalIndex: index,
          operation: createBulkOperation(item, operationType, batch10PartialOperation),
        });
      }
    } else {
      operations.push({
        originalIndex: index,
        operation: createBulkOperation(item, operationType, partialOperations),
      });
    }
  }
  while (i < operations.length) {
    yield operations.slice(i, batchSize + i);
    i += batchSize;
  }
}

export function createBulkOperation(item, operationType, operations) {
  return {
    id: item.id,
    operationType,
    partitionKey: item.partitionKey,
    resourceBody: {
      operations,
    },
  };
}
