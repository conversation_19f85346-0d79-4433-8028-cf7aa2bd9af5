export enum ParcelStatus {
  CANCELLED = 'cancelled',
  PENDING_PICKUP = 'pending_pickup',
  PICKED_UP = 'picked_up',
  BOOKED = 'booked',
  LMD_REJECT_BOOKING = 'lmd_reject_booking',
  LMD_RECEIVE_BOOKING = 'lmd_receive_booking',
  RECEIVED_AT_WAREHOUSE = 'received_at_warehouse',
  PENDING_BOOKING_UPDATES = 'pending_booking_updates',
  EXPIRED = 'expired',
  DEPARTED_FROM_ORIGIN_AIRPORT = 'dep_from_origin_airport',
  ARRIVED_AT_DESTINATION_AIRPORT = 'arr_at_destination_airport',
  PARCEL_HELD_BY_CUSTOMS = 'parcel_held_by_customs',
  ARRIVED_AT_DESTINATION_CUSTOMS_FACILITY = 'arrived_at_destination_custom_facility',
  CUSTOM_CLEARED_AT_DESTINATION = 'custom_cleared',
  ENROUTE_TO_DESTINATION_SORTING_HUB = 'enroute_to_destination_sorting_hub',
  ARRIVE_AT_SORTING_HUB = 'arrived_and_processing_at_sorting_hub',
  OUT_FOR_DELIVERY = 'on_vehicle_for_delivery',
  SUCCESS_DELIVERY = 'successful_delivery',
  UNSUCCESS_DELIVERY = 'delivery_unsuccessful',
  IN_TRANSIT = 'in_transit',
  POSSIBLE_DELAY_FROM_LAST_MILE = 'possible_delay_from_last_mile',
  AWAITING_COLLECTION = 'awaiting_collection',
  SORTED = 'sorted',
  PACKED_TO_CONTAINER = 'packed_to_gaylord',
  CN38_CLOSED = 'cn38_closed',
  MAWB_CLOSED = 'mawb_closed',
  FWB_RECEIVED = 'fwb_received',
  RCS_RECEIVED_BY_AIRLINE = 'rcs_received_by_airline',
  HANDED_OVER_FOR_CUSTOMS_CLEARANCE = 'dlv_shipment_handed_over_to_consignee',
  CONTAINER_READY_TO_CLOSE = 'gaylord_ready_to_close',
  CLOSE_CONTAINER = 'close_gaylord',
  LOST = 'lost',
  DAMAGED = 'damaged',
  FINAL_UNSUCCESSFUL_DELIVERY = 'final_unsuccessful_delivery',
}

export enum CacheName {
  PUBLIC_KEYS = 'public-keys',
  PARCEL_TO_SCAN = 'parcel-to-scan',
  DESTINATION_V2 = 'destinationV2',
  PARCEL_STATUSES = 'parcelStatuses',
  COUNTRIES = 'countries',
}

export const ValidateResult = {
  PARCEL_NOT_FOUND: 'Parcel not found',
  PARCEL_SCANNED_ALREADY: 'Parcel scanned already',
  PARCEL_ACCEPTED: 'Parcel accepted',
  PARCEL_CANCELLED: 'Parcel cancelled',
  PARCEL_ALREADY_PROCESSED: 'Parcel already processed',
  PARCEL_EXPIRED: 'Parcel expired',
  PARCEL_NOT_SAME_MERCHANT: 'Parcel does not belong to this merchant',
};

export const FunctionName = {
  AZ_QUEUE_STORAGE: 'AZURE_QUEUE_STORAGE',
  AZ_KEY_VAULT: 'AZURE_KEY_VAULT',
};

export enum JobTransactionType {
  PICKED_UP = 'picked_up',
}

export enum AppName {
  FMD_API = 'FMD-API',
}

export enum JobType {
  JOB = 'job',
  DRIVER = 'driver',
  COMPANY = 'company',
}
