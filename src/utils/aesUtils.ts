import aesjs from 'aes-js';
import pbkdf2 from 'pbkdf2';
import { AppConfig } from '../resources';
import isObject from 'lodash/isObject';
import isString from 'lodash/isString';
import isArray from 'lodash/isArray';

export function CrtCounterEncrypt(text: string) {
  try {
    const key256 = pbkdf2.pbkdf2Sync(AppConfig.key.aesPassword, AppConfig.key.aesSalt, 1, 256 / 8, 'sha512');
    const textBytes = aesjs.utils.utf8.toBytes(text);
    const aesCtr = new aesjs.ModeOfOperation.ctr(key256, new aesjs.Counter(AppConfig.key.aesCounter));
    const encryptedBytes = aesCtr.encrypt(textBytes);
    return aesjs.utils.hex.fromBytes(encryptedBytes);
  } catch (err) {
    console.log(err);
    return text;
  }
}

export function CrtCounterDecrypt(encryptedHex: string) {
  try {
    const key256 = pbkdf2.pbkdf2Sync(AppConfig.key.aesPassword, AppConfig.key.aesSalt, 1, 256 / 8, 'sha512');
    const aesCtr = new aesjs.ModeOfOperation.ctr(key256, new aesjs.Counter(AppConfig.key.aesCounter));
    const encryptedBytes = aesjs.utils.hex.toBytes(encryptedHex);
    const decryptedBytes = aesCtr.decrypt(encryptedBytes);
    return aesjs.utils.utf8.fromBytes(decryptedBytes);
  } catch (err) {
    console.log(err);
    return encryptedHex;
  }
}

export function encryptArray<T>(array: T[], includes: string[] = []): T[] {
  const encryptedArray = [];

  for (const item of array) {
    if (isString(item)) {
      encryptedArray.push(CrtCounterEncrypt(item));
    } else if (isArray(item)) {
      encryptedArray.push(encryptArray(item, includes));
    } else if (isObject(item)) {
      encryptedArray.push(encryptObject(item, includes));
    }
  }

  return encryptedArray;
}

export function decryptArray<T>(array: T[], includes: string[] = []): T[] {
  const decryptedArray = [];

  for (const item of array) {
    if (isString(item)) {
      decryptedArray.push(CrtCounterDecrypt(item));
      continue;
    }

    if (isArray(item)) {
      decryptedArray.push(decryptArray(item, includes));
      continue;
    }

    if (isObject(item)) {
      decryptedArray.push(decryptObject(item, includes));
    }
  }

  return decryptedArray;
}

export function encryptObject<T>(object: T, includes: string[] = []): T {
  const encryptedObject = {} as T;
  for (const [key, value] of Object.entries(object)) {
    if (includes.includes(key)) {
      if (isString(value)) {
        encryptedObject[key] = CrtCounterEncrypt(value);
      } else if (isArray(value)) {
        encryptedObject[key] = encryptArray(value, includes);
      } else if (isObject(value)) {
        encryptedObject[key] = encryptObject(value, includes);
      }
    } else {
      encryptedObject[key] = value;
    }
  }
  return encryptedObject;
}

export function decryptObject<T>(object: T, includes: string[] = []): T {
  const objArray = Object.entries(object);
  const decryptedObject = {} as T;

  for (const [key, value] of objArray) {
    if (includes.includes(key)) {
      if (isString(value)) {
        decryptedObject[key] = CrtCounterDecrypt(value);
        continue;
      }

      if (isArray(value)) {
        decryptedObject[key] = decryptArray(value, includes);
        continue;
      }

      if (isObject(value)) {
        decryptedObject[key] = decryptObject(value, includes);
      }
    } else {
      decryptedObject[key] = value;
    }
  }

  return decryptedObject;
}
