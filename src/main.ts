import { NestFactory } from '@nestjs/core';
import cookieParser from 'cookie-parser';

import { RedisService } from './shared/RedisService';
import { StatusMappingService } from './shared/StatusMappingService';

import { AppModule } from './app.module';
import { ConfigAppInsights } from './appinsights';
import { loadAppProperties } from './loadAppProperties';
import { MerchantWebHookApi } from './shared/MerchantWebHookApi';

const port = process.env.PORT || 3005;

async function bootstrap() {
  ConfigAppInsights();

  await loadAppProperties();
  await RedisService.init();
  await StatusMappingService.populateStatusMapping();
  MerchantWebHookApi.init();
  const app = await NestFactory.create(AppModule, {
    logger: ['log', 'error', 'warn'],
  });

  app.use(cookieParser());

  app.enableCors();

  await app.listen(port);
}

bootstrap();
