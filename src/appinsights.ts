import * as appInsights from 'applicationinsights';

export function ConfigAppInsights() {
  appInsights
    .setup()
    .setAutoDependencyCorrelation(true)
    .setAutoCollectRequests(true)
    .setAutoCollectPerformance(true)
    .setAutoCollectExceptions(true)
    .setAutoCollectDependencies(true)
    .setAutoCollectConsole(true, true)
    .start();
  appInsights.defaultClient.commonProperties = {
    environment: process.env.NODE_ENV ? process.env.NODE_ENV.toString() : 'DEV',
  };
}
