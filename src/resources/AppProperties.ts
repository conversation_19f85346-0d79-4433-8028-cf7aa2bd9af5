const host = '';
const authKey = '';
const databaseId = 'plsdb';
const manifestItemColId = 'manifest_items';
const manifestItemArchiveColId = 'manifest_items_archive';
const merchant = 'merchant';
const mawb = 'mawb';
const container = 'gaylord';
const aesPassword = '';
const aesCounter = 3;
const aesSalt = 'salt';
const operationHub = 'operation_hub';
const fmd = 'fmd';
const AppConfig = {
  db: {
    host,
    authKey,
    databaseId,
    manifestItemColId,
    manifestItemArchiveColId,
    merchant,
    mawb,
    container,
    operationHub,
    fmd,
  },
  key: {
    aesPassword,
    aesCounter,
    aesSalt,
  },
  azureB2C: {
    client_id: '',
    client_secret: '',
    tenant: '',
    tenantName: process.env.B2C_TENANT || 'devparxl',
    policy: process.env.B2C_POLICY || 'B2C_1_FMD_SignIn',
  },
  finApiUrl: process.env.FINANCE_URL || '',
  pdfFuncUrl: process.env.PDF_FUNC_URL || '',
  keyvaultCredential: {},
  redis: {
    host: process.env.REDISCACHEHOSTNAME || '',
    port: 6380,
    authKey: '',
  },
};
export { AppConfig };
