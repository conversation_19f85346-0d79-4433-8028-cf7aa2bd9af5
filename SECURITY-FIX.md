# FMD API Security Fix: Object-Level Authorization

## Overview

This document describes the security vulnerability fix implemented for the FMD API to address critical object-level authorization issues that allowed users to access resources belonging to other users or from different countries.

## Vulnerabilities Addressed

### 1. Manifest Document Access (Scenario 1)
- **Issue**: User "<PERSON>" could access manifest document SG056 belonging to "Kevin Traders Express"
- **Endpoint**: `POST /fmd/manifest-document`
- **Root Cause**: No validation that the job belongs to the requesting user

### 2. Cross-Country Parcel Access (Scenario 2)
- **Issue**: User "kevin korea" (Korea account) could view parcels in Singapore
- **Endpoint**: `GET /fmd/pickup-parcels/SG017`
- **Root Cause**: No country-based access control validation

### 3. Cross-Country Pickup Address Access (Scenario 3)
- **Issue**: Users could access pickup addresses from other countries
- **Endpoints**: 
  - `GET /fmd/a8308af1-83ab-0874-0ea5-c26a64fca180/pickup-addresses` (Korea)
  - `GET /fmd/8cab9163-59cc-4599-8a8a-492e144b8251/pickup-addresses` (Singapore)
- **Root Cause**: No validation that company ID belongs to user's company

### 4. Cross-Country Parcel Scanning (Scenario 4)
- **Issue**: Korea user could scan parcels belonging to Singapore users
- **Endpoint**: `POST /fmd/scan-parcel`
- **Root Cause**: No country validation for merchant account access

## Solution Architecture

### Authorization Guard Implementation

The fix implements a new `FmdAuthorizationGuard` that provides object-level authorization:

```typescript
@Injectable()
export class FmdAuthorizationGuard implements CanActivate {
  // Validates user access based on authorization type
  async canActivate(context: ExecutionContext): Promise<boolean>
}
```

### Authorization Types

```typescript
export enum FmdAuthorizationType {
  COMPANY_RESOURCE = 'COMPANY_RESOURCE',    // Company-owned resources
  MERCHANT_RESOURCE = 'MERCHANT_RESOURCE',  // Country-based merchant access
  JOB_RESOURCE = 'JOB_RESOURCE',           // User-owned jobs
  PARCEL_SCAN = 'PARCEL_SCAN',             // Parcel scanning operations
}
```

### Decorator Usage

```typescript
@Get(':id/pickup-addresses')
@FmdAuthorize(FmdAuthorizationType.COMPANY_RESOURCE)
async getPickupAddresses(@Param('id') companyId: string) {
  return this.fmdService.getMerchantPickupAddressListForCompany(companyId);
}
```

## Implementation Details

### 1. Authorization Context Building

For each request, the guard builds an authorization context:

```typescript
interface FmdAuthorizationContext {
  userEmail: string;
  driverCompanyId: string;
  companyCountry: string;
}
```

### 2. Validation Logic

#### Company Resource Validation
- Validates that the requested company ID matches the user's company ID
- Prevents cross-company resource access

#### Merchant Resource Validation
- Retrieves merchant information and validates country
- Ensures users can only access merchants from their own country
- Uses country code normalization for accurate comparison

#### Job Resource Validation
- Validates that the job belongs to the requesting user
- Prevents access to jobs created by other drivers

#### Parcel Scan Validation
- Validates merchant country before allowing parcel scanning
- Ensures drivers can only scan parcels for merchants in their country

### 3. Error Handling

The guard provides specific error messages for different authorization failures:

- `"Access denied: You can only access resources from your own company"`
- `"Access denied: You can only access merchants from your country"`
- `"Access denied: You can only access your own jobs"`

## Security Testing

### Unit Tests
- Comprehensive unit tests for all authorization scenarios
- Mock data setup for different countries and companies
- Edge case testing for invalid data

### Integration Tests
- End-to-end testing of all vulnerable endpoints
- Verification of both positive and negative test cases
- Cross-country access attempt validation

### Security Verification Script
- Automated script to verify all security fixes
- Tests against actual API endpoints
- Provides detailed reporting of security status

## Running Security Tests

```bash
# Run unit tests for authorization guard
npm test -- fmd-authorization.guard.spec.ts

# Run integration security tests
npm run test:security

# Run security verification script
npm run verify:security
```

## Backward Compatibility

The implementation maintains full backward compatibility:

- ✅ No changes to existing API contracts
- ✅ No changes to service method signatures
- ✅ No database schema modifications
- ✅ Existing functionality remains unchanged
- ✅ Only adds authorization validation layer

## Performance Impact

- **Minimal**: Authorization checks use existing database queries
- **Cached**: Driver and company information can be cached
- **Efficient**: Single database lookup per request for authorization context

## Deployment Verification

After deployment, verify the fix using the security verification script:

```bash
# Verify against UAT environment
npm run verify:security

# Expected result: All tests should pass with 403 Forbidden for unauthorized access
```

## Monitoring and Alerting

Consider implementing monitoring for:

- Authorization failure rates
- Cross-country access attempts
- Unusual access patterns
- Failed authorization attempts by user

## Future Enhancements

1. **Audit Logging**: Log all authorization decisions for security auditing
2. **Rate Limiting**: Implement rate limiting for failed authorization attempts
3. **Advanced Monitoring**: Set up alerts for suspicious access patterns
4. **Permission Caching**: Cache authorization decisions to improve performance

## Conclusion

This security fix successfully addresses all four identified vulnerabilities while maintaining backward compatibility and system performance. The implementation follows established patterns in the codebase and provides comprehensive testing to ensure reliability.
