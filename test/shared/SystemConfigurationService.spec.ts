import axios from 'axios';
import SystemConfigurationService from './../../src/shared/SystemConfigurationService';
import { RedisService } from './../../src/shared/RedisService';

jest.mock('axios');
jest.mock('./../../src/shared/RedisService');

describe('SystemConfigurationService', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getSystemConfigurations', () => {
    it('should return system configurations from Redis cache', async () => {
      // Arrange
      const cachedConfig = { versioning_configurations: [{ name: 'featureFlag', selected_version: 'ON' }] };
      jest.spyOn(RedisService, 'getCache').mockResolvedValue(cachedConfig);

      // Act
      const result = await SystemConfigurationService.getSystemConfigurations();

      // Assert
      expect(result).toEqual(cachedConfig.versioning_configurations);
      expect(axios.get).not.toHaveBeenCalled();
    });

    it('should fetch system configurations from FIN service when not cached', async () => {
      // Arrange
      const mockResponse = {
        data: {
          success: true,
          message: { versioning_configurations: [{ name: 'featureFlag', selected_version: 'ON' }] },
        },
      };
      jest.spyOn(axios, 'get').mockResolvedValue(mockResponse);
      jest.spyOn(RedisService, 'getCache').mockResolvedValue(null);

      // Act
      const result = await SystemConfigurationService.getSystemConfigurations();

      // Assert
      expect(result).toEqual(mockResponse.data.message.versioning_configurations);
      expect(axios.get).toHaveBeenCalledWith(`${process.env.FINANCE_URL}/system-configurations`);
    });

    it('should return an empty array when not cached and FIN service fails', async () => {
      // Arrange
      const errorResponse = {
        data: { success: false, message: 'Error message' },
      };
      jest.spyOn(axios, 'get').mockResolvedValue(errorResponse);
      jest.spyOn(RedisService, 'getCache').mockResolvedValue(null);

      // Act
      const result = await SystemConfigurationService.getSystemConfigurations();

      // Assert
      expect(result).toEqual([]);
      expect(axios.get).toHaveBeenCalledWith(`${process.env.FINANCE_URL}/system-configurations`);
      expect(result).not.toHaveProperty('versioning_configurations');
    });
  });

  describe('isFlagEnabled', () => {
    it('should return true when the flag is enabled', async () => {
      // Arrange
      const flagName = 'featureFlag';
      const config = [{ name: flagName, selected_version: 'ON' }];
      SystemConfigurationService.getSystemConfigurations = jest.fn().mockResolvedValue(config);

      // Act
      const result = await SystemConfigurationService.isFlagEnabled(flagName);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false when the flag is disabled', async () => {
      // Arrange
      const flagName = 'featureFlag';
      const config = [{ name: flagName, selected_version: 'OFF' }];
      SystemConfigurationService.getSystemConfigurations = jest.fn().mockResolvedValue(config);

      // Act
      const result = await SystemConfigurationService.isFlagEnabled(flagName);

      // Assert
      expect(result).toBe(false);
    });

    it('should return the default value when the flag is not found', async () => {
      // Arrange
      const flagName = 'nonExistentFlag';
      const defaultValue = true;
      const config = [{ name: 'featureFlag', selected_version: 'ON' }];
      SystemConfigurationService.getSystemConfigurations = jest.fn().mockResolvedValue(config);

      // Act
      const result = await SystemConfigurationService.isFlagEnabled(flagName, defaultValue);

      // Assert
      expect(result).toBe(defaultValue);
    });

    it('should return the default value when system configurations are not available', async () => {
      // Arrange
      const flagName = 'featureFlag';
      const defaultValue = true;
      SystemConfigurationService.getSystemConfigurations = jest.fn().mockResolvedValue(null);

      // Act
      const result = await SystemConfigurationService.isFlagEnabled(flagName, defaultValue);

      // Assert
      expect(result).toBe(defaultValue);
    });
  });
});
