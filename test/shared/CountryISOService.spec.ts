import { RedisService } from './../../src/shared/RedisService';
import { CountryISOService } from './../../src/shared/CountryISOService';
import axios from 'axios';
jest.mock('./../../src/shared/RedisService');
jest.mock('axios');

const service = new CountryISOService();
console.error = jest.fn();
console.log = jest.fn();
axios.get = jest.fn().mockResolvedValue({ data: { success: false } });

beforeEach(() => {
  jest.clearAllMocks();
});
describe('Test country iso service', () => {
  describe('Test findCountry function', () => {
    test('When country is US, return value as expected', () => {
      //Assign
      service.countriesList = [
        {
          codeAlpha2: 'US',
          codeAlpha3: 'USA',
          name: 'America',
        },
        {
          codeAlpha2: 'VN',
          codeAlpha3: 'VNA',
          name: 'Vietnam',
        },
      ];

      //Act
      const result = service.findCountry('AMERICA');

      //Assert
      expect(result.name).toEqual('America');
    });

    test('When country is non US, return value as expected', () => {
      //Assign
      service.countriesList = [
        {
          codeAlpha2: 'SG',
          codeAlpha3: 'SIN',
          name: 'Singapore',
        },
        {
          codeAlpha2: 'VN',
          codeAlpha3: 'VNA',
          name: 'Vietnam',
        },
      ];

      //Act
      const result = service.findCountry('Vietnam');

      //Assert
      expect(result.name).toEqual('Vietnam');
    });
  });

  describe('Test getCountriesList function', () => {
    test('When there is no country list, return empty result', async () => {
      //Assign
      jest.spyOn(RedisService, 'getCache').mockResolvedValue([]);
      jest.spyOn(axios, 'get').mockResolvedValue({ data: { success: false } });

      //Act
      const result = await service.getCountriesList();

      //Assert
      expect(result.length).toEqual(0);
      expect(console.log).toHaveBeenCalledTimes(2);
    });

    test('When having there is no country list, return value as expected', async () => {
      //Assign
      jest.spyOn(RedisService, 'getCache').mockResolvedValue([]);
      jest.spyOn(axios, 'get').mockResolvedValue({ data: { success: true, data: [{ name: 'Singapore' }] } });

      //Act
      const results = await service.getCountriesList();

      //Assert
      expect(results[0].name).toEqual('Singapore');
    });
  });

  describe('Test checkCache functoin', () => {
    test('When there is already country list, do nothing', async () => {
      //Assign
      service.countriesList = [{ name: 'Singapore', codeAlpha2: 'SG', codeAlpha3: 'SIN' }];
      service.getCountriesList = jest.fn();

      //Act
      await service.checkCache();

      //Assert
      expect(service.getCountriesList).not.toHaveBeenCalled();
    });

    test('When there is not country list, log the error', async () => {
      //Assign
      service.countriesList = [];
      service.getCountriesList = jest.fn().mockResolvedValue([]);

      //Act
      await service.checkCache();

      //Assert
      expect(service.getCountriesList).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalled();
    });

    test('When there is country list, return value as expeceted', async () => {
      //Assign
      service.countriesList = [];
      service.getCountriesList = jest
        .fn()
        .mockResolvedValue({ name: 'Singapore', codeAlpha2: 'SG', codeAlpha3: 'SIN' });

      //Act
      await service.checkCache();

      //Assert
      expect(service.countriesList[0].name).toEqual('Singapore');
    });
  });
});
