import MockDate from 'mockdate';
import { MerchantDaoService } from '../../src/db/merchant/merchant-dao.service';
import { MerchantWebHookApi } from './../../src/shared/MerchantWebHookApi';
import { AzStorageQueue } from '../../src/shared/AzQueueStorageService';
import { StatusMappingService } from '../../src/shared/StatusMappingService';
import { ParcelStatus } from '../../src/utils/Enum';
import { getSecretValue } from '../../src/shared/KeyVaultService';

const mockDate = new Date('2020-07-17T04:09:48.372Z');
MockDate.set(mockDate);
jest.mock('../../src/db/merchant/merchant-dao.service.ts', () => {
  return {
    MerchantDaoService: jest.fn().mockImplementation(() => ({})),
  };
});
jest.mock('../../src/shared/KeyVaultService', () => {
  return {
    getSecretValue: jest.fn().mockImplementation(() => ({})),
  };
});

console.log = jest.fn();

describe('Test updateStatus + getMerchantApiUrl function', () => {
  const parcel = {
    id: '1',
    tracking_id: '1',
    merchant_account_number: '123',
  };
  let merchantDao: MerchantDaoService;

  beforeEach(() => {
    merchantDao = {
      find: jest.fn(),
    } as unknown as MerchantDaoService;
    MerchantWebHookApi.merchantDaoService = merchantDao;
    StatusMappingService.getStatus = jest.fn((manifestStt) => ({
      manifestStt,
      blockchain: manifestStt,
      isMerchantVisible: true,
    }));
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  it('should initialize merchantDaoService as an instance of MerchantDaoService', () => {
    // Arrange
    MerchantWebHookApi.merchantDaoService = undefined; // Clear any existing instance

    // Act
    MerchantWebHookApi.init();

    // Assert
    expect(MerchantWebHookApi.merchantDaoService).toBeInstanceOf(Object);
  });

  it('getMerchantApiUrl should return api url if expire > Date.now()', async () => {
    // Arrange
    MerchantWebHookApi.merchantCache['abc'] = {
      expiresOn: new Date(**************),
      api_url: 'abc.xyc',
    };

    // Act
    const result = await MerchantWebHookApi.getMerchantApiUrl('abc');

    // Assert
    expect(result).toBe('abc.xyc');
  });

  it('getMerchantApiUrl return merchant.api_url', async () => {
    // Arrange
    MerchantWebHookApi.merchantCache['abc'] = {
      expiresOn: new Date(),
      api_url: 'abc.xyc',
    };
    MerchantWebHookApi.merchantDaoService.find = jest.fn().mockResolvedValue([
      {
        expiresOn: new Date(),
        api_url: 'abc.xyc',
        merchant_account_number: 'abc',
      },
    ]);

    // Act
    const result = await MerchantWebHookApi.getMerchantApiUrl('abc');

    // Assert
    expect(result).toBe('abc.xyc');
  });

  it('getMerchantApiUrl return null if found no merchant', async () => {
    // Arrange
    MerchantWebHookApi.merchantCache['abc'] = {
      expiresOn: new Date(),
      api_url: 'abc.xyc',
    };
    MerchantWebHookApi.merchantDaoService.find = jest.fn().mockResolvedValue([]);

    // Act
    const result = await MerchantWebHookApi.getMerchantApiUrl('abc');

    // Assert
    expect(result).toBe(null);
  });

  it('getMerchantApiUrl return null if found no merchant secret', async () => {
    // Arrange
    MerchantWebHookApi.merchantCache['abc'] = {
      expiresOn: new Date(),
      api_url: 'abc.xyc',
    };
    MerchantWebHookApi.merchantDaoService.find = jest.fn().mockResolvedValue([
      {
        expiresOn: new Date(),
        api_url: 'abc.xyc',
        merchant_account_number: 'abc',
      },
    ]);
    (getSecretValue as jest.Mock) = jest.fn().mockResolvedValue(undefined);

    // Act
    const result = await MerchantWebHookApi.getMerchantApiUrl('abc');

    // Assert
    expect(result).toBe(null);
  });

  it('should return false if blockchain status not found', async () => {
    // Arrange
    AzStorageQueue.sendBase64Message = jest.fn();
    StatusMappingService.getStatus = jest.fn().mockResolvedValueOnce({
      isMerchantVisible: true,
    });

    // Act
    const res = await MerchantWebHookApi.updateStatus(parcel, ParcelStatus.BOOKED, new Date());

    // Assert
    expect(res?.success).toBeFalsy();
    expect(AzStorageQueue.sendBase64Message).not.toHaveBeenCalled();
  });

  it('should return false if status is excluded from the webhook', async () => {
    // Arrange
    StatusMappingService.getStatus = jest.fn((manifestStt) => ({
      manifestStt,
      blockchain: manifestStt,
      isMerchantVisible: false,
    }));
    AzStorageQueue.sendBase64Message = jest.fn();

    // Act
    const res = await MerchantWebHookApi.updateStatus(parcel, ParcelStatus.PICKED_UP, new Date());

    // Assert
    expect(res?.success).toBeFalsy();
    expect(AzStorageQueue.sendBase64Message).not.toHaveBeenCalled();
  });

  it('should return false if merchant has no webhook url', async () => {
    // Arrange
    MerchantWebHookApi.getMerchantApiUrl = jest.fn().mockResolvedValueOnce('');
    AzStorageQueue.sendBase64Message = jest.fn();

    // Act
    const res = await MerchantWebHookApi.updateStatus(parcel, ParcelStatus.PICKED_UP, new Date());

    // Assert
    expect(res?.success).toBeFalsy();
    expect(MerchantWebHookApi.getMerchantApiUrl).toHaveBeenCalledTimes(1);
    expect(AzStorageQueue.sendBase64Message).not.toHaveBeenCalled();
  });

  it('should return false if isMerchantVisible false', async () => {
    // Arrange
    MerchantWebHookApi.getMerchantApiUrl = jest.fn().mockResolvedValueOnce('webhook');
    AzStorageQueue.sendBase64Message = jest.fn();
    StatusMappingService.getStatus = jest.fn().mockReturnValueOnce({
      isMerchantVisible: false,
      blockchain: 'a',
    });

    // Act
    const res = await MerchantWebHookApi.updateStatus(parcel, ParcelStatus.PICKED_UP, new Date());

    // Assert
    expect(res?.success).toBeFalsy();
    expect(MerchantWebHookApi.getMerchantApiUrl).toHaveBeenCalledTimes(1);
    expect(AzStorageQueue.sendBase64Message).not.toHaveBeenCalled();
  });

  it('should push to queue to update blockchain status', async () => {
    // Arrange
    MerchantWebHookApi.getMerchantApiUrl = jest.fn().mockResolvedValueOnce('webhook');
    AzStorageQueue.sendBase64Message = jest.fn();
    const mockParcel = {
      id: '1',
      merchant_account_number: '123',
    };

    // Act
    const res = await MerchantWebHookApi.updateStatus(mockParcel, ParcelStatus.PICKED_UP, new Date());

    // Assert
    expect(res?.success).toBeTruthy();
    expect(MerchantWebHookApi.getMerchantApiUrl).toHaveBeenCalledTimes(1);
    expect(AzStorageQueue.sendBase64Message).toHaveBeenCalledTimes(1);
  });

  it('should return Error due to unable to push data to queue', async () => {
    // Arrange
    const errMsg = 'Failed';
    const expResult = {
      success: false,
      message: errMsg,
    };

    MerchantWebHookApi.getMerchantApiUrl = jest.fn().mockResolvedValueOnce('webhook');
    AzStorageQueue.sendBase64Message = jest.fn().mockRejectedValueOnce(errMsg);

    // Act
    const res = await MerchantWebHookApi.updateStatus(parcel, ParcelStatus.PICKED_UP, new Date());

    // Assert
    expect(res).toEqual(expResult);
  });
});
