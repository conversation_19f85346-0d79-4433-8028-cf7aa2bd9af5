import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { FmdDriverDaoService } from '../src/db/fmd/fmd-driver.dao.service';
import { FmdCompanyDaoService } from '../src/db/fmd/fmd-company.dao.service';
import { MerchantDaoService } from '../src/db/merchant/merchant-dao.service';

describe('FMD Security (e2e)', () => {
  let app: INestApplication;
  let fmdDriverDaoService: FmdDriverDaoService;
  let fmdCompanyDaoService: FmdCompanyDaoService;
  let merchantDaoService: MerchantDaoService;

  // Mock JWT tokens for different users
  const singaporeUserToken = 'Bearer mock-singapore-user-token';
  const koreaUserToken = 'Bearer mock-korea-user-token';

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    fmdDriverDaoService = moduleFixture.get<FmdDriverDaoService>(FmdDriverDaoService);
    fmdCompanyDaoService = moduleFixture.get<FmdCompanyDaoService>(FmdCompanyDaoService);
    merchantDaoService = moduleFixture.get<MerchantDaoService>(MerchantDaoService);

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    await app.close();
  });

  async function setupTestData() {
    // Mock Singapore company and driver
    jest.spyOn(fmdCompanyDaoService, 'getItem').mockImplementation(async (id) => {
      if (id === 'singapore-company') {
        return {
          id: 'singapore-company',
          company: 'Singapore FMD Company',
          country: 'Singapore',
          type: 'company',
        };
      }
      if (id === 'korea-company') {
        return {
          id: 'korea-company',
          company: 'Korea FMD Company',
          country: 'Korea',
          type: 'company',
        };
      }
      return null;
    });

    jest.spyOn(fmdDriverDaoService, 'getDriver').mockImplementation(async (email) => {
      if (email === '<EMAIL>') {
        return {
          id: 'singapore-driver',
          name: 'Singapore Driver',
          email: '<EMAIL>',
          companyId: 'singapore-company',
        };
      }
      if (email === '<EMAIL>') {
        return {
          id: 'korea-driver',
          name: 'Korea Driver',
          email: '<EMAIL>',
          companyId: 'korea-company',
        };
      }
      return null;
    });

    // Mock merchant data
    jest.spyOn(merchantDaoService, 'find').mockImplementation(async (query) => {
      const merchantAccountNo = query.parameters?.find(p => p.name === '@merchantAccountNo')?.value;
      
      if (merchantAccountNo === 'encrypted_SG017') {
        return [{
          id: 'singapore-merchant',
          merchant_account_number: 'encrypted_SG017',
          country: 'encrypted_Singapore',
        }];
      }
      if (merchantAccountNo === 'encrypted_KR001') {
        return [{
          id: 'korea-merchant',
          merchant_account_number: 'encrypted_KR001',
          country: 'encrypted_Korea',
        }];
      }
      return [];
    });
  }

  describe('Scenario 1: Manifest Document Access', () => {
    it('should deny access to manifest document from different company', async () => {
      const response = await request(app.getHttpServer())
        .post('/fmd/manifest-document')
        .set('Authorization', koreaUserToken)
        .send({
          jobId: 'singapore-job-123',
          parcelIds: ['parcel1', 'parcel2'],
        });

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });
  });

  describe('Scenario 2: Cross-Country Parcel Access', () => {
    it('should deny Korea user access to Singapore parcels', async () => {
      const response = await request(app.getHttpServer())
        .get('/fmd/pickup-parcels/SG017')
        .set('Authorization', koreaUserToken);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('You can only access merchants from your country');
    });

    it('should allow Singapore user access to Singapore parcels', async () => {
      const response = await request(app.getHttpServer())
        .get('/fmd/pickup-parcels/SG017')
        .set('Authorization', singaporeUserToken);

      expect(response.status).toBe(200);
    });
  });

  describe('Scenario 3: Cross-Country Pickup Address Access', () => {
    it('should deny access to pickup addresses from different company', async () => {
      const response = await request(app.getHttpServer())
        .get('/fmd/singapore-company/pickup-addresses')
        .set('Authorization', koreaUserToken);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('You can only access resources from your own company');
    });

    it('should allow access to pickup addresses from same company', async () => {
      const response = await request(app.getHttpServer())
        .get('/fmd/singapore-company/pickup-addresses')
        .set('Authorization', singaporeUserToken);

      expect(response.status).toBe(200);
    });
  });

  describe('Scenario 4: Cross-Country Parcel Scanning', () => {
    it('should deny Korea user from scanning Singapore parcels', async () => {
      const response = await request(app.getHttpServer())
        .post('/fmd/scan-parcel')
        .set('Authorization', koreaUserToken)
        .send({
          jobId: 'korea-job-123',
          inMerchantPickupList: false,
          parcelBarcode: 'SG-PARCEL-123',
          merchantAccountNo: 'SG017',
        });

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('You can only access merchants from your country');
    });

    it('should allow Korea user to scan Korea parcels', async () => {
      const response = await request(app.getHttpServer())
        .post('/fmd/scan-parcel')
        .set('Authorization', koreaUserToken)
        .send({
          jobId: 'korea-job-123',
          inMerchantPickupList: false,
          parcelBarcode: 'KR-PARCEL-123',
          merchantAccountNo: 'KR001',
        });

      expect(response.status).toBe(200);
    });
  });

  describe('Authorization Bypass Attempts', () => {
    it('should prevent parameter manipulation attacks', async () => {
      // Attempt to access Singapore company with Korea user token
      const response = await request(app.getHttpServer())
        .get('/fmd/singapore-company/pickup-addresses')
        .set('Authorization', koreaUserToken);

      expect(response.status).toBe(403);
    });

    it('should prevent merchant account number manipulation', async () => {
      // Attempt to access Singapore merchant with Korea user token
      const response = await request(app.getHttpServer())
        .get('/fmd/pickup-parcels/SG017')
        .set('Authorization', koreaUserToken);

      expect(response.status).toBe(403);
    });

    it('should prevent job ID manipulation in manifest document creation', async () => {
      // Attempt to create manifest for Singapore job with Korea user token
      const response = await request(app.getHttpServer())
        .post('/fmd/manifest-document')
        .set('Authorization', koreaUserToken)
        .send({
          jobId: 'singapore-job-123',
          parcelIds: ['parcel1'],
        });

      expect(response.status).toBe(403);
    });
  });
});
