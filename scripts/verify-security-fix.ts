#!/usr/bin/env ts-node

/**
 * Security Verification Script for FMD API Authorization Fixes
 * 
 * This script verifies that the object-level authorization fixes prevent
 * all four identified security vulnerabilities:
 * 1. Manifest Document Access (Cross-company)
 * 2. Cross-Country Parcel Access
 * 3. Cross-Country Pickup Address Access  
 * 4. Cross-Country Parcel Scanning
 */

import axios, { AxiosResponse } from 'axios';

interface TestCase {
  name: string;
  description: string;
  endpoint: string;
  method: 'GET' | 'POST';
  headers: Record<string, string>;
  body?: any;
  expectedStatus: number;
  expectedMessage?: string;
}

class SecurityVerifier {
  private baseUrl: string;
  private testResults: Array<{ testCase: string; passed: boolean; details: string }> = [];

  constructor(baseUrl: string = 'https://fmd-api-uat.parxl.com') {
    this.baseUrl = baseUrl;
  }

  async runAllTests(): Promise<void> {
    console.log('🔒 Starting FMD API Security Verification...\n');

    const testCases: TestCase[] = [
      // Scenario 1: Manifest Document Access
      {
        name: 'Manifest Document Cross-Company Access',
        description: 'Korea user should not access Singapore manifest documents',
        endpoint: '/fmd/manifest-document',
        method: 'POST',
        headers: { 'Authorization': 'Bearer korea-user-token' },
        body: { jobId: 'singapore-job-SG056', parcelIds: ['parcel1'] },
        expectedStatus: 403,
        expectedMessage: 'Access denied: You can only access your own jobs',
      },

      // Scenario 2: Cross-Country Parcel Access
      {
        name: 'Cross-Country Parcel Access',
        description: 'Korea user should not access Singapore parcels',
        endpoint: '/fmd/pickup-parcels/SG017',
        method: 'GET',
        headers: { 'Authorization': 'Bearer korea-user-token' },
        expectedStatus: 403,
        expectedMessage: 'Access denied: You can only access merchants from your country',
      },

      // Scenario 3: Cross-Country Pickup Address Access (Korea to Singapore)
      {
        name: 'Cross-Country Pickup Address Access (Korea→Singapore)',
        description: 'Korea user should not access Singapore pickup addresses',
        endpoint: '/fmd/8cab9163-59cc-4599-8a8a-492e144b8251/pickup-addresses',
        method: 'GET',
        headers: { 'Authorization': 'Bearer korea-user-token' },
        expectedStatus: 403,
        expectedMessage: 'Access denied: You can only access resources from your own company',
      },

      // Scenario 3: Cross-Country Pickup Address Access (Singapore to Korea)
      {
        name: 'Cross-Country Pickup Address Access (Singapore→Korea)',
        description: 'Singapore user should not access Korea pickup addresses',
        endpoint: '/fmd/a8308af1-83ab-0874-0ea5-c26a64fca180/pickup-addresses',
        method: 'GET',
        headers: { 'Authorization': 'Bearer singapore-user-token' },
        expectedStatus: 403,
        expectedMessage: 'Access denied: You can only access resources from your own company',
      },

      // Scenario 4: Cross-Country Parcel Scanning
      {
        name: 'Cross-Country Parcel Scanning',
        description: 'Korea user should not scan Singapore parcels',
        endpoint: '/fmd/scan-parcel',
        method: 'POST',
        headers: { 'Authorization': 'Bearer korea-user-token' },
        body: {
          jobId: 'korea-job-123',
          inMerchantPickupList: false,
          parcelBarcode: 'SG-PARCEL-123',
          merchantAccountNo: 'SG017',
        },
        expectedStatus: 403,
        expectedMessage: 'Access denied: You can only access merchants from your country',
      },

      // Positive test cases to ensure legitimate access still works
      {
        name: 'Legitimate Singapore User Access',
        description: 'Singapore user should access Singapore resources',
        endpoint: '/fmd/pickup-parcels/SG017',
        method: 'GET',
        headers: { 'Authorization': 'Bearer singapore-user-token' },
        expectedStatus: 200,
      },

      {
        name: 'Legitimate Korea User Access',
        description: 'Korea user should access Korea resources',
        endpoint: '/fmd/pickup-parcels/KR001',
        method: 'GET',
        headers: { 'Authorization': 'Bearer korea-user-token' },
        expectedStatus: 200,
      },
    ];

    for (const testCase of testCases) {
      await this.runTestCase(testCase);
    }

    this.printResults();
  }

  private async runTestCase(testCase: TestCase): Promise<void> {
    console.log(`🧪 Testing: ${testCase.name}`);
    console.log(`   ${testCase.description}`);

    try {
      const response: AxiosResponse = await axios({
        method: testCase.method,
        url: `${this.baseUrl}${testCase.endpoint}`,
        headers: testCase.headers,
        data: testCase.body,
        validateStatus: () => true, // Don't throw on non-2xx status codes
      });

      const passed = response.status === testCase.expectedStatus;
      let details = `Status: ${response.status} (expected: ${testCase.expectedStatus})`;

      if (testCase.expectedMessage && response.data?.message) {
        const messageMatch = response.data.message.includes(testCase.expectedMessage);
        details += `, Message match: ${messageMatch}`;
        
        if (!messageMatch && passed) {
          // Status matches but message doesn't - this might indicate a different type of error
          details += ` (Got: "${response.data.message}")`;
        }
      }

      this.testResults.push({
        testCase: testCase.name,
        passed,
        details,
      });

      console.log(`   ${passed ? '✅ PASSED' : '❌ FAILED'}: ${details}\n`);

    } catch (error) {
      this.testResults.push({
        testCase: testCase.name,
        passed: false,
        details: `Error: ${error.message}`,
      });

      console.log(`   ❌ FAILED: ${error.message}\n`);
    }
  }

  private printResults(): void {
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;

    console.log('📊 SECURITY VERIFICATION RESULTS');
    console.log('='.repeat(50));
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%\n`);

    if (passed === total) {
      console.log('🎉 ALL SECURITY TESTS PASSED!');
      console.log('✅ The authorization fixes successfully prevent all identified vulnerabilities.');
    } else {
      console.log('⚠️  SOME TESTS FAILED!');
      console.log('❌ The following vulnerabilities may still exist:');
      
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   - ${r.testCase}: ${r.details}`);
        });
    }

    console.log('\n🔒 Security verification complete.');
  }
}

// Run the verification if this script is executed directly
if (require.main === module) {
  const verifier = new SecurityVerifier();
  verifier.runAllTests().catch(console.error);
}

export { SecurityVerifier };
