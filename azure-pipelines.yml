trigger:
  batch: true
  branches:
    include:
    - release/*
    - develop

pool:
  vmImage: ubuntu-latest

steps:
- task: NodeTool@0
  inputs:
    versionSpec: 20.x
  displayName: Install Node.js 20

- script: npm audit --audit-level=high
  displayName: Audit dependencies
  continueOnError: true

- script: npm ci
  displayName: 'npm install'

- script: |
    npm test
  displayName: Unit test

- script: |
    npm run build
  displayName: 'npm build'

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '$(System.DefaultWorkingDirectory)/dist'
    includeRootFolder: false
    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId)-fmd-api.zip' 
    replaceExistingArchive: true

- task: PublishBuildArtifacts@1
  inputs:
    pathtoPublish: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId)-fmd-api.zip'
    artifactName: PlsFMDApi
